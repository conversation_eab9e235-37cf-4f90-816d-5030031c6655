<template>
  <div class="editable-result-table-container">
    <!-- 编辑操作按钮栏 - 隐藏按钮组，因为已经移到顶部 -->
    <!-- <div class="table-actions"> -->
      <!-- 编辑模式下显示的按钮组已移到顶部工具栏 -->
      <!-- <div class="action-left" v-if="!props.isReadOnly">
        <el-button type="primary" size="small" @click="handleSubmitChanges">
          <el-icon><Check /></el-icon>
          提交修改
        </el-button>
        <el-button type="success" size="small" @click="handleAddRow">
          <el-icon><Plus /></el-icon>
          新增
        </el-button>
        <el-button type="danger" size="small" @click="handleDeleteSelected" :disabled="selectedRowKeys.size === 0">
          <el-icon><Delete /></el-icon>
          删除
        </el-button>
        <el-button type="info" size="small" @click="handleCancelEdit">
          <el-icon><Close /></el-icon>
          取消编辑
        </el-button>
      </div> -->
      
      <!-- <div class="table-info"> -->
        <!-- 隐藏记录总数显示，因为上层已经有更详细的统计信息 -->
        <!-- <span class="table-count">共 {{ tableData?.length || 0 }} 条记录</span> -->
        <!-- <span v-if="pendingChanges && pendingChanges.length > 0" class="pending-count">
          {{ pendingChanges.length }} 项待提交
        </span> -->
      <!-- </div> -->
    <!-- </div> -->
    
    <!-- 表格内容区 -->
    <div class="table-wrapper" ref="tableContainerRef">
      <el-table-v2
        v-if="isContainerReady"
        :columns="tableColumns"
        :data="tableData"
        :width="Math.max(width || 800, 800)"
        :height="Math.max(height > 0 ? height - 80 : 500, 500)"
        :row-height="40"
        :header-height="45"
        :row-class="getRowClassName"
        :fixed="true"
      >
        <template #empty>
          <div class="el-table-v2__empty">
            <span>没有数据</span>
          </div>
        </template>
      </el-table-v2>

      <!-- 调试信息 -->
      <div v-else class="table-loading">
        <span>等待表格准备...</span>
        <div class="loading-info">
          容器尺寸: {{ width }} x {{ height }}
        </div>
        <div class="loading-info">
          容器准备状态: {{ isContainerReady }}
        </div>
        <div class="loading-info">
          数据行数: {{ tableData?.length || 0 }}
        </div>
        <div class="loading-info">
          表格高度: {{ Math.max(height > 0 ? height - 80 : 400, 400) }}
        </div>
        <div class="loading-info">
          数据总数: {{ tableData?.length || 0 }}
        </div>
        <div class="loading-info">
          props.data长度: {{ props.data?.length || 0 }}
        </div>
        <div class="loading-info">
          props.columns长度: {{ props.columns?.length || 0 }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, h, watch, nextTick, onMounted } from 'vue';
import { ElMessage, ElMessageBox, ElInput, ElCheckbox } from 'element-plus';
import { useElementSize } from '@vueuse/core';
import { Plus, Delete, Edit, Check, Close } from '@element-plus/icons-vue';

const props = defineProps({
  data: { type: Array, required: true },
  columns: { type: Array, required: true },
  isReadOnly: { type: Boolean, default: false },
  isClickhouse: { type: Boolean, default: false },
  tabIndex: { type: [String, Number], default: 'main' }
});

const emit = defineEmits(['submit:changes', 'cell-dblclick', 'update-cell-value', 'generate-backup-sql', 'pending-changes-updated', 'toggle-edit-mode', 'selection-changed']);

const tableContainerRef = ref(null);
const { width, height } = useElementSize(tableContainerRef);

// 添加一个标志来确保表格在容器尺寸获取后才渲染
const isContainerReady = ref(false);

// 计算表格的实际高度
const tableHeight = computed(() => {
  if (height.value > 100) {
    return height.value;
  }
  // 如果容器高度不足，使用一个合理的默认值
  return Math.max(400, height.value);
});

// 计算自适应表格高度
const calculateTableHeight = computed(() => {
  const rowHeight = 40; // 每行大约42px高度（稍微增加）
  const headerHeight = 45; // 表头高度（稍微增加）
  const dataLength = tableData.value.length;

  // 计算内容所需的高度，加上更多额外空间
  const contentHeight = headerHeight + (dataLength * rowHeight) + 50; // 额外50px缓冲

  // 对于分页数据，确保至少能显示一页的内容
  const minHeightForPage = headerHeight + (20 * rowHeight) + 50; // 20行的高度
  const minHeight = Math.max(400, minHeightForPage);
  const maxHeight = 1200; // 增加最大高度

  // 始终使用内容高度，但确保至少能显示一页
  const calculatedHeight = Math.max(minHeight, Math.min(contentHeight, maxHeight));

  console.log(`表格高度计算: 数据行数=${dataLength}, 内容高度=${contentHeight}, 最小高度=${minHeight}, 最终高度=${calculatedHeight}`);

  return calculatedHeight;
});

// 在组件挂载后检查容器尺寸
onMounted(() => {
  // 立即设置为准备状态，避免等待尺寸导致的问题
  isContainerReady.value = true;
  console.log('表格组件已挂载，设置为准备状态');
  console.log('挂载时的props.data:', props.data);
  console.log('挂载时的props.columns:', props.columns);
  console.log('挂载时的tableData:', tableData.value);

  // 初始化数据
  if (props.data && props.data.length > 0) {
    console.log('挂载时有数据，初始化表格');
    reloadData(props.data, false);
  }

  // 延迟检查容器尺寸，确保DOM完全渲染
  setTimeout(() => {
    if (width.value === 0 || height.value === 0) {
      console.log('容器尺寸为0，强制设置为准备状态');
      isContainerReady.value = true;
    }
    console.log('延迟检查 - 容器尺寸:', width.value, 'x', height.value);
    console.log('延迟检查 - tableData长度:', tableData.value.length);
    console.log('延迟检查 - isContainerReady:', isContainerReady.value);
  }, 100);
});

// 监听尺寸变化，确保表格能够正确渲染
watch([width, height], ([newWidth, newHeight]) => {
  console.log(`容器尺寸变化: ${newWidth} x ${newHeight}`);
  // 无论尺寸如何都设置为准备状态，避免因为尺寸问题导致表格不显示
  isContainerReady.value = true;
  if (newWidth > 0 && newHeight > 0) {
    console.log(`表格容器尺寸更新: ${newWidth} x ${newHeight}`);
  } else {
    console.log('容器尺寸为0，但仍然设置为准备状态');
  }
}, { immediate: true });

const tableData = ref([]);
const originalDataMap = new Map();
const pendingChanges = ref([]);
const selectedRowKeys = ref(new Set());
let rowKeyCounter = 0;
let keyGenerationCounter = 0;

// 添加一个标志来跟踪是否是用户主动提交后的模式切换
const isSubmittingChanges = ref(false);

// 用于存储行对象到键的映射
const rowKeyMap = new WeakMap();

const getRowKey = (row) => {
  if (!row) {
    console.error("严重错误: 尝试获取null或undefined行的键");
    return `null_key_${Date.now()}`;
  }

  // 如果行对象有__v_key属性，优先使用它
  if (row.__v_key !== undefined) {
    return row.__v_key;
  }

  // 如果已经为这个行对象分配了键，重用它
  if (rowKeyMap.has(row)) {
    return rowKeyMap.get(row);
  }

  // 生成基于行内容的稳定键
  let stableKey;
  try {
    // 使用行内容的哈希值生成稳定键，确保唯一性
    const contentStr = Object.entries(row)
      .filter(([key, value]) => key !== '__v_key' && value !== null && value !== undefined)
      .sort(([a], [b]) => a.localeCompare(b))
      .map(([key, value]) => {
        try {
          const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
          return `${key}:${valueStr}`;
        } catch (e) {
          return `${key}:[object]`;
        }
      })
      .join('|');

    if (contentStr) {
      const hash = hashCode(contentStr);
      stableKey = `content_${hash}`;
    } else {
      // 最后的备选方案
      stableKey = `fallback_${++keyGenerationCounter}`;
    }
  } catch (e) {
    console.error("生成行键时出错:", e);
    stableKey = `error_key_${++keyGenerationCounter}`;
  }

  // 为行对象添加__v_key属性
  row.__v_key = stableKey;

  // 同时在WeakMap中存储，以便在行对象被修改后仍能找到原始键
  rowKeyMap.set(row, stableKey);

  return stableKey;
};

// 简单的字符串哈希函数
function hashCode(str) {
  let hash = 0;
  if (str.length === 0) return hash;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32bit integer
  }
  return Math.abs(hash).toString(16);
}

const updateCellValue = (row, columnKey, value) => {
  if (!row || columnKey === undefined || columnKey === null) {
    console.error('updateCellValue: 缺少行或列键');
    return;
  }

  const rowKey = getRowKey(row);
  console.log(`=== 开始更新单元格值 ===`);
  console.log(`行键=${rowKey}, 列=${columnKey}, 新值=${value}`);
  console.log(`传入的行数据:`, row);

  // 检查是否有多个行使用相同的键
  const rowsWithSameKey = tableData.value.filter(r => getRowKey(r) === rowKey);
  if (rowsWithSameKey.length > 1) {
    console.warn(`警告: ${rowsWithSameKey.length} 行使用相同键 ${rowKey}，这可能导致数据混乱`);
    rowsWithSameKey.forEach((r, index) => {
      console.log(`  行 ${index + 1}:`, JSON.stringify(r));
    });
  }

  const originalRow = originalDataMap.get(rowKey);
  
  // 检查是否为新添加的行
  const isNewRow = pendingChanges.value.some(c => c.type === 'add' && c.rowKey === rowKey);
  
  if (!originalRow && !isNewRow) {
    console.error(`更新失败：在原始数据中找不到key为 ${rowKey} 的行。`);
    return;
  }

  // 如果是新行，直接更新值
  if (isNewRow) {
    const targetRow = tableData.value.find(r => getRowKey(r) === rowKey);
    if (targetRow) {
      targetRow[columnKey] = value;
      
      // 查找该行在待变更列表中的索引
      const addChangeIndex = pendingChanges.value.findIndex(c => 
        c.type === 'add' && c.rowKey === rowKey
      );
      
      if (addChangeIndex !== -1) {
        // 更新新行的数据
        pendingChanges.value[addChangeIndex].row[columnKey] = value;
        console.log('已更新新行的字段值:', { rowKey, columnKey, value });
      }
      
      emit('pending-changes-updated', pendingChanges.value);
    }
    return;
  }

  // 以下是处理现有行的更新逻辑
  const originalValue = originalRow[columnKey];
  
  if (String(originalValue) === String(value)) {
    console.log('值未变化，不产生变更记录');
    const changeIndex = pendingChanges.value.findIndex(c => 
      c.type === 'update' && c.rowKey === rowKey && c.columnKey === columnKey
    );
    if (changeIndex !== -1) {
      pendingChanges.value.splice(changeIndex, 1);
      emit('pending-changes-updated', pendingChanges.value);
    }
    return;
  }

  const targetRow = tableData.value.find(r => getRowKey(r) === rowKey);
  if (targetRow) {
    // 直接更新表格数据，确保显示正确的值
    targetRow[columnKey] = value;
    console.log(`已更新表格数据: 行键=${rowKey}, 列=${columnKey}, 新值=${value}`);

    const change = {
      type: 'update',
      row: targetRow, // 使用表格中的实际行对象
      rowKey,
      columnKey,
      columnName: columnKey, // 添加columnName字段
      newValue: value,
      originalValue,
    };
    
    const existingChangeIndex = pendingChanges.value.findIndex(c => 
      c.type === 'update' && c.rowKey === rowKey && c.columnKey === columnKey
    );

    if (existingChangeIndex !== -1) {
      pendingChanges.value[existingChangeIndex] = change;
      console.log('更新现有变更记录:', change);
    } else {
      pendingChanges.value.push(change);
      console.log('添加新变更记录:', change);
    }

    console.log('当前所有待变更记录:', pendingChanges.value.map(c => ({
      type: c.type,
      rowKey: c.rowKey,
      columnKey: c.columnKey,
      newValue: c.newValue
    })));

    emit('pending-changes-updated', pendingChanges.value);
  }
};

const handleAddRow = () => {
  if (props.isReadOnly) {
    ElMessage.warning('当前处于只读模式，无法添加数据');
    return;
  }

  const newKey = rowKeyCounter++;
  const newRow = { __v_key: newKey };
  props.columns.forEach(col => {
    if (col && col.prop) {
      newRow[col.prop] = ''; // 使用空字符串而不是null，这样在表格中更容易看到和编辑
    }
  });

  tableData.value.unshift(newRow);
  originalDataMap.set(newKey, {});

  pendingChanges.value.push({ 
    type: 'add', 
    rowKey: newKey,
    row: newRow 
  });
  
  emit('pending-changes-updated', pendingChanges.value);

  // 强制刷新表格视图
  nextTick(() => {
    // 确保表格容器存在
    if (tableContainerRef.value) {
      tableContainerRef.value.scrollTop = 0;
      
      // 强制更新表格数据引用，触发视图更新
      tableData.value = [...tableData.value];
      console.log('新行已添加，强制更新表格视图', tableData.value);
    }
  });
};

const handleDeleteSelected = () => {
  if (props.isReadOnly) {
    ElMessage.warning('当前处于只读模式，无法删除数据');
    return;
  }
  
  if (selectedRowKeys.value.size === 0) {
    ElMessage.warning('请先选择要删除的行');
    return;
  }
  
  const selectedKeys = Array.from(selectedRowKeys.value);

  selectedKeys.forEach(key => {
    const isNewRow = pendingChanges.value.some(c => c.type === 'add' && c.rowKey === key);
    if (!isNewRow) {
      const rowToDelete = tableData.value.find(r => getRowKey(r) === key);
      if (rowToDelete) {
         pendingChanges.value.push({ type: 'delete', rowKey: key, row: rowToDelete });
      }
    }
  });

  pendingChanges.value = pendingChanges.value.filter(c => 
    !(c.type === 'add' && selectedKeys.includes(c.rowKey))
  );

  tableData.value = tableData.value.filter(row => !selectedKeys.includes(getRowKey(row)));

  selectedRowKeys.value.clear();
  // 通知父组件选择状态变化
  emit('selection-changed', 0);
  emit('pending-changes-updated', pendingChanges.value);
};

const handleCancelEdit = () => {
  if (pendingChanges.value.length > 0) {
    ElMessageBox.confirm('取消编辑将丢弃所有未提交的更改，是否继续？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      pendingChanges.value = [];
      reloadData(props.data, false); // 取消编辑时清空选择状态
      emit('toggle-edit-mode', true);
    }).catch(() => {});
  } else {
    emit('toggle-edit-mode', true);
  }
};

const handleSubmitChanges = () => {
  console.log('EditableResultTable.handleSubmitChanges被调用，当前只读状态:', props.isReadOnly);
  
  // 如果处于只读模式，先尝试切换到编辑模式
  if (props.isReadOnly) {
    console.log('当前处于只读模式，通知父组件切换到编辑模式');
    // 通知父组件切换编辑模式
    emit('toggle-edit-mode', false); // false表示不是只读模式
    
    // 如果没有待提交的修改，添加一个空的修改以便能继续流程
    if (pendingChanges.value.length === 0) {
      console.log('没有待提交的修改，但仍然继续流程');
      // 不直接返回，继续执行后续逻辑
    }
  } else if (pendingChanges.value.length === 0) {
    console.log('编辑模式下没有待提交的修改');
    ElMessage.warning('没有待提交的修改');
    return;
  }
  
  const changesToSubmit = JSON.parse(JSON.stringify(pendingChanges.value));
  
  // 先触发生成备份SQL事件
  emit('generate-backup-sql', {
    changes: changesToSubmit,
    callback: (backupSql, executionSql) => {
      // 如果没有生成有效的备份SQL或执行SQL，直接提交
      if (!backupSql && !executionSql) {
        console.warn('没有生成备份SQL或执行SQL，直接提交变更');
        emit('submit:changes', changesToSubmit);
        return;
      }
      
      // 格式化SQL以便高亮显示
      const formatSql = (sql) => {
        if (!sql) return '无SQL语句';
        
        // 处理空白行，保留换行格式
        sql = sql.replace(/\n/g, '<br/>');
        
        // 添加语法高亮
        return sql
          // SQL关键字高亮 (蓝色粗体)
          .replace(/\b(SELECT|FROM|WHERE|UPDATE|DELETE|INSERT|INTO|VALUES|SET|AND|OR|JOIN|LEFT|RIGHT|INNER|OUTER|CROSS|ON|GROUP BY|ORDER BY|HAVING|LIMIT|OFFSET|CREATE|ALTER|DROP|TRUNCATE|TABLE|DATABASE|SCHEMA|INDEX|VIEW|PROCEDURE|FUNCTION|TRIGGER|CASE|WHEN|THEN|ELSE|END|AS|DISTINCT|BETWEEN|IN|IS|NULL|NOT|EXISTS|LIKE|ASC|DESC)\b/gi, 
                  '<span style="color:#409EFF;font-weight:bold;">$1</span>')
          // 表名和列名高亮 (红色)
          .replace(/(\`[^\`]+\`)/g, '<span style="color:#F56C6C;">$1</span>')
          // 字符串高亮 (绿色)
          .replace(/('[^']*')/g, '<span style="color:#67C23A;">$1</span>')
          // 数字高亮 (橙色)
          .replace(/\b(\d+)\b/g, '<span style="color:#E6A23C;">$1</span>')
          // 注释高亮 (灰色斜体)
          .replace(/(--.*?)(<br\/>|$)/g, '<span style="color:#909399;font-style:italic;">$1</span>$2')
          // 括号高亮 (深色)
          .replace(/(\(|\))/g, '<span style="color:#606266;font-weight:bold;">$1</span>');
      };
      
      // 弹出美化的确认对话框，显示备份SQL和执行SQL
      ElMessageBox.confirm(
        `<div style="max-height:500px;overflow:auto;font-family:'Courier New', monospace;">
          <div style="margin-bottom:16px;border-radius:8px;overflow:hidden;box-shadow:0 2px 12px 0 rgba(0,0,0,0.1);transition:all 0.3s ease;">
            <div style="background:linear-gradient(135deg, #409EFF, #007bff);color:white;padding:12px 15px;font-weight:bold;display:flex;align-items:center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right:8px;">
                <path d="M8 0c-.69 0-1.843.265-2.928.56-1.11.3-2.229.655-2.887.87a1.54 1.54 0 0 0-1.044 1.262c-.596 4.477.787 7.795 2.465 9.99a11.8 11.8 0 0 0 2.517 2.453c.386.273.744.482 1.048.625.28.132.581.24.829.24s.548-.108.829-.24a7 7 0 0 0 1.048-.625 11.8 11.8 0 0 0 2.517-2.453c1.678-2.195 3.061-5.513 2.465-9.99a1.54 1.54 0 0 0-1.044-1.263 62.5 62.5 0 0 0-2.887-.87C9.843.266 8.69 0 8 0m0 5a1.5 1.5 0 0 1 1.5 1.5A1.5 1.5 0 0 1 8 8a1.5 1.5 0 0 1-1.5-1.5A1.5 1.5 0 0 1 8 5m0 5.5a1 1 0 1 1 0 2 1 1 0 0 1 0-2"/>
              </svg>
              <span>即将执行以下SQL</span>
            </div>
            <div style="background:#f8f8f8;padding:15px;border:1px solid #e0e0e0;max-height:180px;overflow:auto;">
              <pre style="margin:0;white-space:pre-wrap;word-break:break-word;color:#303133;line-height:1.6;">${formatSql(executionSql || '无执行SQL')}</pre>
            </div>
          </div>
          
          <div style="margin-bottom:10px;border-radius:8px;overflow:hidden;box-shadow:0 2px 12px 0 rgba(0,0,0,0.1);transition:all 0.3s ease;">
            <div style="background:linear-gradient(135deg, #E6A23C, #d48806);color:white;padding:12px 15px;font-weight:bold;display:flex;align-items:center;">
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16" style="margin-right:8px;">
                <path d="M8.5 11.5a.5.5 0 0 1-1 0V7.707L6.354 8.854a.5.5 0 1 1-.708-.708l2-2a.5.5 0 0 1 .708 0l2 2a.5.5 0 0 1-.708.708L8.5 7.707z"/>
                <path d="M14 14V4.5L9.5 0H4a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2M9.5 3A1.5 1.5 0 0 0 11 4.5h2V14a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V2a1 1 0 0 1 1-1h5.5z"/>
              </svg>
              <span>备份SQL</span>
            </div>
            <div style="background:#f8f8f8;padding:15px;border:1px solid #e0e0e0;max-height:180px;overflow:auto;">
              <pre style="margin:0;white-space:pre-wrap;word-break:break-word;color:#303133;line-height:1.6;">${formatSql(backupSql || '无备份SQL')}</pre>
            </div>
          </div>
          
          <div style="background:#f0f9eb;border-radius:4px;padding:12px;margin-top:16px;border-left:4px solid #67C23A;display:flex;align-items:flex-start;">
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="#67C23A" viewBox="0 0 16 16" style="margin-right:10px;flex-shrink:0;margin-top:2px;">
              <path d="M8 16A8 8 0 1 0 8 0a8 8 0 0 0 0 16m.93-9.412-1 4.705c-.07.34.029.533.304.533.194 0 .487-.07.686-.246l-.088.416c-.287.346-.92.598-1.465.598-.703 0-1.002-.422-.808-1.319l.738-3.468c.064-.293.006-.399-.287-.47l-.451-.081.082-.381 2.29-.287zM8 5.5a1 1 0 1 1 0-2 1 1 0 0 1 0 2"/>
            </svg>
            <div>
              <p style="margin:0;color:#67C23A;font-weight:bold;">提示</p>
              <p style="margin:5px 0 0;color:#606266;font-size:13px;line-height:1.5;">请确认以上SQL执行是否符合您的预期，执行前请确保已备份重要数据。</p>
            </div>
          </div>
        </div>`,
        '确认提交修改',
        {
          confirmButtonText: '确认执行',
          cancelButtonText: '取消',
          type: 'warning',
          dangerouslyUseHTMLString: true,
          customClass: 'sql-confirm-dialog',
          distinguishCancelAndClose: true,
          closeOnClickModal: false
        }
      ).then(() => {
        // 用户确认后，先应用当前修改到表格数据，然后退出编辑模式
        console.log('用户确认提交，应用修改并退出编辑模式');

        // 1. 设置提交标志，防止 isReadOnly watcher 清空数据
        isSubmittingChanges.value = true;

        // 2. 先应用当前的修改到表格数据中
        applyPendingChangesToTableData();

        // 3. 然后通知父组件切换到只读模式
        emit('toggle-edit-mode', true, props.tabIndex); // true表示切换到只读模式

        // 4. 最后提交变更
        emit('submit:changes', changesToSubmit);

        ElMessage.success('修改已提交，正在处理...');
      }).catch((action) => {
        if (action === 'cancel') {
          ElMessage.info('已取消提交');
        }
      });
    }
  });
};

const reloadData = (newData, preserveSelection = true) => {
  if (!Array.isArray(newData)) {
    console.error('Data reload failed: newData is not an array.');
    tableData.value = [];
    return;
  }

  // 保存当前的选择状态（如果需要保持选择）
  const currentSelectedKeys = preserveSelection ? new Set(selectedRowKeys.value) : new Set();
  console.log('重新加载数据，保持选择状态:', preserveSelection, '当前选择数量:', currentSelectedKeys.size);

  // 保存当前的新增行（待提交的添加操作）
  const addedRows = [];
  pendingChanges.value.forEach(change => {
    if (change.type === 'add' && change.row) {
      addedRows.push(change.row);
    }
  });

  // 保存现有行的键映射（基于行内容）
  const existingKeyMap = new Map();
  tableData.value.forEach(row => {
    // 创建一个不包含__v_key的内容哈希，用于匹配
    const { __v_key, ...contentOnly } = row;
    const contentHash = JSON.stringify(contentOnly);
    const rowKey = getRowKey(row);
    existingKeyMap.set(contentHash, rowKey);

    // 同时创建一个基于主键字段的映射（更稳定的匹配方式）
    // 假设comment_id是主键或唯一标识
    if (contentOnly.comment_id !== undefined) {
      const primaryKeyHash = `pk_${contentOnly.comment_id}_${contentOnly.craftsman_name || ''}_${contentOnly.employee_no || ''}`;
      existingKeyMap.set(primaryKeyHash, rowKey);
    }
  });
  console.log(`保存了 ${existingKeyMap.size} 个现有行的键映射`);

  // 不重置 rowKeyCounter，保持键的唯一性
  // rowKeyCounter = 0; // 注释掉这行，避免键重复
  originalDataMap.clear();

  // 处理数据前先清理现有的WeakMap
  // (WeakMap会自动垃圾回收，但这里为了安全起见，我们先清空tableData)
  tableData.value = [];
  
  tableData.value = newData.map((row, index) => {
    if (!row) {
      console.warn('发现null或undefined行，已跳过');
      return null;
    }

    try {
      // 创建行的副本，避免修改原始数据
      const processedRow = { ...row };

      // 简化逻辑：直接使用getRowKey函数，它会处理键的生成和复用
      const rowKey = getRowKey(processedRow);


      // 存储原始行数据，用于后续比较变更
      originalDataMap.set(rowKey, { ...row });


      return processedRow;
    } catch (e) {
      console.error('处理行数据时出错:', e, row);
      // 返回带有错误标记的行，而不是返回null
      return { 
        __v_key: `error_row_${rowKeyCounter++}`, 
        __error: true, 
        ...row 
      };
    }
  }).filter(Boolean); // 过滤掉null行

  // 重新添加新增行到表格顶部
  if (addedRows.length > 0) {
    tableData.value.unshift(...addedRows);
  }

  // 恢复选择状态（如果需要保持选择）
  if (preserveSelection && currentSelectedKeys.size > 0) {
    selectedRowKeys.value.clear();
    // 尝试恢复选择状态，通过匹配rowKey
    tableData.value.forEach(row => {
      const rowKey = getRowKey(row);
      if (currentSelectedKeys.has(rowKey)) {
        selectedRowKeys.value.add(rowKey);
      }
    });
    console.log(`已恢复选择状态，恢复了 ${selectedRowKeys.value.size} 个选择项`);

    // 通知父组件选择状态变化
    if (selectedRowKeys.value.size > 0) {
      emit('selection-changed', selectedRowKeys.value.size);
    }
  } else {
    selectedRowKeys.value.clear();
  }

  console.log(`已重新加载${tableData.value.length}行数据（包含${addedRows.length}个新增行），选择状态: ${selectedRowKeys.value.size}`);
};

const updateTableData = (newData) => {
  // console.log('手动更新表格数据:', newData.length);
  if (!Array.isArray(newData)) {
    console.error('更新失败: newData不是数组');
    return;
  }
  
  // 清空现有状态
  pendingChanges.value = [];
  selectedRowKeys.value.clear();
  
  // 重新加载数据
  reloadData(newData, false); // 手动更新表格数据时清空选择状态
  
  // 强制刷新视图
  nextTick(() => {
    console.log('表格数据已更新，行数:', tableData.value.length);
  });
};

watch(() => props.data, (newData) => {
  // 如果正在提交修改，不要重新加载数据
  if (isSubmittingChanges.value) {
    console.log('正在提交修改，跳过数据重新加载');
    return;
  }

  console.log('props.data changed, reloading table.');
  console.log('新数据长度:', newData?.length || 0);
  console.log('新数据内容:', newData);
  const oldChanges = [...pendingChanges.value];
  reloadData(newData, true); // props.data变化时保持选择状态

  // 重新映射待变更记录的行键
  if (oldChanges.length > 0) {
    console.log('重新映射待变更记录的行键，原始数量:', oldChanges.length);
    const updatedChanges = [];

    oldChanges.forEach(change => {
      if (change.type === 'update' && change.row) {
        // 在新的表格数据中找到对应的行
        // 通过生成原始行的内容哈希来匹配
        const originalContentStr = Object.entries(change.row)
          .filter(([key, value]) => key !== '__v_key' && key !== change.columnKey && value !== null && value !== undefined)
          .sort(([a], [b]) => a.localeCompare(b))
          .map(([key, value]) => {
            try {
              const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
              return `${key}:${valueStr}`;
            } catch (e) {
              return `${key}:[object]`;
            }
          })
          .join('|');

        const matchingRow = tableData.value.find(row => {
          const currentContentStr = Object.entries(row)
            .filter(([key, value]) => key !== '__v_key' && key !== change.columnKey && value !== null && value !== undefined)
            .sort(([a], [b]) => a.localeCompare(b))
            .map(([key, value]) => {
              try {
                const valueStr = typeof value === 'object' ? JSON.stringify(value) : String(value);
                return `${key}:${valueStr}`;
              } catch (e) {
                return `${key}:[object]`;
              }
            })
            .join('|');

          return currentContentStr === originalContentStr;
        });

        if (matchingRow) {
          // 应用之前的变更到匹配的行
          matchingRow[change.columnKey] = change.newValue;

          const newRowKey = getRowKey(matchingRow);
          console.log(`重新映射变更记录: ${change.rowKey} -> ${newRowKey}`);
          updatedChanges.push({
            ...change,
            rowKey: newRowKey,
            row: matchingRow
          });
        } else {
          console.warn('无法找到匹配的行，丢弃变更记录:', change);
        }
      } else {
        // 保持其他类型的变更记录
        updatedChanges.push(change);
      }
    });

    pendingChanges.value = updatedChanges;
    console.log('重新映射后的待变更记录数量:', updatedChanges.length);
  }
}, { deep: true, immediate: true });

watch(() => props.isReadOnly, (isReadOnly) => {
  if (isReadOnly && pendingChanges.value.length > 0 && !isSubmittingChanges.value) {
    // 只有在非提交状态下才清空修改和重新加载数据
    pendingChanges.value = [];
    reloadData(props.data, false); // 切换到只读模式时清空选择状态
    ElMessage.info('已切换到只读模式，所有未提交的修改已被丢弃。');
  } else if (isReadOnly && isSubmittingChanges.value) {
    // 如果是提交后的模式切换，重置标志但不清空数据
    console.log('用户提交后切换到只读模式，保留已应用的修改');
    isSubmittingChanges.value = false;
  }
});

const getRowClassName = ({ rowData }) => {
  const rowKey = getRowKey(rowData);
  if (pendingChanges.value.some(c => c.type === 'add' && c.rowKey === rowKey)) {
    return 'new-row';
  }
  if (pendingChanges.value.some(c => c.type === 'update' && c.rowKey === rowKey)) {
    return 'modified-row';
  }
  return '';
};

const handleDblClick = (rowData, columnKey) => {
  // 检查是否点击了选择框或序号列，如果是则不处理双击事件
  if (columnKey === '__selection' || columnKey === '__index') return;
  
  // 无论是否只读都触发双击事件，但将只读状态传递给父组件
  emit('cell-dblclick', rowData, columnKey, props.isReadOnly);
};

const cellRenderer = ({ rowData, column }) => {
  const columnKey = column.key;
  const cellData = rowData[columnKey];
  const currentRowKey = getRowKey(rowData);

  const pendingChange = pendingChanges.value.find(c =>
    c.type === 'update' && c.rowKey === currentRowKey && c.columnKey === columnKey
  );

  // 调试：检查待变更记录
  // if (columnKey === 'nickname') { // 只对nickname字段输出调试信息，避免日志过多
  //   console.log(`=== 单元格渲染调试 ===`);
  //   console.log(`行键: ${currentRowKey}, 列: ${columnKey}`);
  //   console.log(`行数据:`, rowData);
  //   console.log(`行数据中的__v_key:`, rowData.__v_key);
  //   console.log(`找到待变更记录: ${!!pendingChange}`);

  //   if (pendingChange) {
  //     console.log(`待变更详情:`, pendingChange);
  //   }

  //   // 列出所有相关的待变更记录
  //   const relatedChanges = pendingChanges.value.filter(c =>
  //     c.type === 'update' && c.columnKey === columnKey
  //   );
  //   console.log(`所有${columnKey}字段的待变更记录:`, relatedChanges);

  //   // 列出所有待变更记录
  //   console.log(`所有待变更记录:`, pendingChanges.value);

  //   // 检查键匹配情况
  //   if (relatedChanges.length > 0) {
  //     console.log(`当前行键 ${currentRowKey} 是否匹配待变更记录:`,
  //       relatedChanges.some(c => c.rowKey === currentRowKey));
  //     console.log(`待变更记录的键:`, relatedChanges.map(c => c.rowKey));

  //     // 详细比较每个键
  //     relatedChanges.forEach((change, index) => {
  //       console.log(`变更记录 ${index}: 键="${change.rowKey}", 当前行键="${currentRowKey}", 匹配=${change.rowKey === currentRowKey}`);
  //     });
  //   }

  //   console.log(`=== 单元格渲染调试结束 ===`);
  // }

  // 检查是否有键冲突（仅在发现问题时输出警告）
  if (pendingChange) {
    const rowsWithSameKey = tableData.value.filter(r => getRowKey(r) === currentRowKey);
    if (rowsWithSameKey.length > 1) {
      console.warn(`发现键冲突: ${rowsWithSameKey.length} 行使用相同键 ${currentRowKey}`);
    }
  }

  const displayValue = pendingChange ? pendingChange.newValue : cellData;
  const isChanged = !!pendingChange;
  const isNewRow = pendingChanges.value.some(c => c.type === 'add' && c.rowKey === currentRowKey);
  
  let formattedValue;
  if (displayValue === null || displayValue === undefined) {
    formattedValue = 'NULL';
  } else if (displayValue === '' && isNewRow) {
    formattedValue = '(点击编辑)'; // 新增行的空字段显示占位符
  } else {
    formattedValue = String(displayValue);
  }
  
  return h('div', {
    class: { 
      'cell-content': true, 
      'changed-cell': isChanged, 
      'null-value': displayValue === null || displayValue === undefined,
      'new-row-cell': isNewRow
    },
    onDblclick: () => handleDblClick(rowData, columnKey),
    style: { cursor: props.isReadOnly ? 'default' : 'pointer' }
  }, formattedValue);
};

const selectionHeaderRenderer = () => h(ElCheckbox, {
  checked: tableData.value.length > 0 && selectedRowKeys.value.size === tableData.value.length,
  indeterminate: selectedRowKeys.value.size > 0 && selectedRowKeys.value.size < tableData.value.length,
  onChange: (checked) => {
    if (checked) {
      tableData.value.forEach(row => selectedRowKeys.value.add(getRowKey(row)));
    } else {
      selectedRowKeys.value.clear();
    }
    // 触发响应式更新
    selectedRowKeys.value = new Set(selectedRowKeys.value);
    // 通知父组件选择状态变化
    emit('selection-changed', selectedRowKeys.value.size);
  }
});

const selectionCellRenderer = ({ rowData }) => {
  const rowKey = getRowKey(rowData);
  return h(ElCheckbox, {
    checked: selectedRowKeys.value.has(rowKey),
    onChange: (checked) => {
      if (checked) {
        selectedRowKeys.value.add(rowKey);
      } else {
        selectedRowKeys.value.delete(rowKey);
      }
      // 触发响应式更新
      selectedRowKeys.value = new Set(selectedRowKeys.value);
      // 通知父组件选择状态变化
      emit('selection-changed', selectedRowKeys.value.size);
    }
  });
};

const tableColumns = computed(() => {
  console.log('计算tableColumns，props.columns:', props.columns);
  const columns = [
    { key: '__selection', width: 50, cellRenderer: selectionCellRenderer, headerCellRenderer: selectionHeaderRenderer, align: 'center' },
    { key: '__index', width: 60, title: '#', cellRenderer: ({ rowIndex }) => `${rowIndex + 1}`, align: 'center' },
    ...props.columns.map(col => ({
      key: col.prop,
      dataKey: col.prop,
      title: col.label,
      width: 200,
      cellRenderer,
    }))
  ];
  console.log('计算得到的tableColumns:', columns);
  return columns;
});

const handleUpdateCellValue = (eventData) => {
  console.log('EditableResultTable收到单元格更新事件:', eventData);
  
  if (!eventData || !eventData.data) {
    console.error('无效的单元格更新事件数据:', eventData);
    return;
  }
  
  const { row, columnKey, value, originalValue } = eventData.data;
  
  if (!row || !columnKey) {
    console.error('单元格更新事件缺少必要参数:', { row, columnKey, value });
    return;
  }
  
  try {
    // 确保行有有效的键
    if (!row.__v_key) {
      console.log('行没有__v_key，尝试生成键');
      // 使用getRowKey函数获取或生成键
      const rowKey = getRowKey(row);
      
      // 如果行没有__v_key属性，添加它
      if (row.__v_key !== rowKey) {
        row.__v_key = rowKey;
        console.log(`已为行添加__v_key: ${rowKey}`);
      }
    }
    
    // 查找表格中匹配的行
    // 首先尝试使用行的现有键（如果有的话）
    const incomingRowKey = row.__v_key || getRowKey(row);
    let targetRow = tableData.value.find(r => getRowKey(r) === incomingRowKey);

    console.log(`查找目标行: 传入行键=${incomingRowKey}, 找到匹配=${!!targetRow}`);
    
    // 如果找不到匹配的行，尝试通过内容匹配
    if (!targetRow) {
      console.warn('通过键找不到匹配行，尝试通过内容匹配');
      
      // 创建一个匹配函数，比较行的关键字段
      const matchRow = (r1, r2) => {
        if (!r1 || !r2) return false;
        
        // 尝试匹配常见的ID字段
        if (r1.id && r1.id === r2.id) return true;
        if (r1.ID && r1.ID === r2.ID) return true;
        if (r1.comment_id && r1.comment_id === r2.comment_id) return true;
        if (r1.employee_no && r1.employee_no === r2.employee_no) return true;
        
        // 尝试匹配所有非空字段（排除可能已经被修改的字段）
        const fields1 = Object.entries(r1).filter(([k, v]) =>
          v !== null && v !== undefined && k !== '__v_key' && k !== columnKey);
        
        if (fields1.length === 0) return false;
        
        // 如果大部分字段匹配，认为是同一行
        const matchCount = fields1.filter(([k, v]) => 
          r2[k] !== undefined && r2[k] !== null && r2[k] === v).length;
        
        return matchCount >= Math.max(1, fields1.length * 0.8);
      };
      
      targetRow = tableData.value.find(r => matchRow(r, row));
      
      // 如果找到匹配的行，更新键映射
      if (targetRow) {
        console.log('通过内容找到匹配行，更新键映射');
        const targetKey = getRowKey(targetRow);
        console.log(`键映射更新: ${incomingRowKey} -> ${targetKey}`);
        row.__v_key = targetKey;
        rowKeyMap.set(row, targetKey);
      } else {
        console.warn('无法找到匹配的行，可能导致更新失败');
        console.log('传入的行数据:', JSON.stringify(row));
        console.log('表格中的行数据示例:', tableData.value.length > 0 ? JSON.stringify(tableData.value[0]) : '无数据');
      }
    }
    
    // 调用现有的updateCellValue方法更新单元格
    if (targetRow) {
      // 使用找到的目标行
      updateCellValue(targetRow, columnKey, value);
      console.log('使用目标行更新单元格值:', { rowKey: getRowKey(targetRow), columnKey, value });
    } else {
      console.error('无法找到目标行，无法更新单元格');
      ElMessage.error('无法找到要更新的行，请重试');
      return;
    }

    console.log('单元格值已更新:', { columnKey, value });
  } catch (error) {
    console.error('处理单元格更新事件时出错:', error);
    ElMessage.error(`更新单元格失败: ${error.message}`);
  }
};

// 应用待提交的更改到表格数据中
const applyPendingChangesToTableData = () => {
  console.log('应用待提交的更改到表格数据中', pendingChanges.value);

  pendingChanges.value.forEach(change => {
    if (change.type === 'update') {
      // 查找对应的行
      const rowIndex = tableData.value.findIndex(row => {
        // 使用行的唯一标识符来匹配
        if (row.__v_key && change.row.__v_key) {
          return row.__v_key === change.row.__v_key;
        }
        // 如果没有__v_key，使用其他字段匹配
        return Object.keys(change.row).every(key => {
          if (key === change.columnName) return true; // 跳过正在修改的字段
          return row[key] === change.row[key];
        });
      });

      if (rowIndex !== -1) {
        // 应用修改
        tableData.value[rowIndex][change.columnName] = change.newValue;
        console.log(`已应用修改: 行${rowIndex}, 字段${change.columnName}, 新值:`, change.newValue);
      } else {
        console.warn('未找到要修改的行:', change.row);
      }
    }
    // 可以扩展处理其他类型的更改（add, delete等）
  });

  // 强制触发响应式更新
  tableData.value = [...tableData.value];
  console.log('已应用所有待提交的更改到表格数据');
};

// 清空待提交的更改（在提交成功后调用）
const clearPendingChanges = () => {
  console.log('清空待提交的更改');
  pendingChanges.value = [];
  emit('pending-changes-updated', pendingChanges.value);

  // 重置提交标志，允许后续的数据重新加载
  isSubmittingChanges.value = false;
  console.log('已重置提交标志，允许数据重新加载');
};

defineExpose({
  handleSubmitChanges,
  handleAddRow,
  handleDeleteSelected,
  handleCancelEdit,
  updateTableData,
  pendingChanges,
  tableData,
  selectedRowKeys,
  handleUpdateCellValue,
  clearPendingChanges
});
</script>

<style>
.editable-result-table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 控制内部滚动 */
  flex: 1;
  min-height: 600px; /* 设置最小高度，确保表格容器可见 */
}

.table-actions {
  padding: 4px 8px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  border-bottom: 1px solid #d9d9d9;
  background-color: #fafafa;
  position: sticky;
  top: 0;
  z-index: 10;
  min-height: 24px;
}

.action-left {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: nowrap;
  flex-shrink: 0;
  margin-right: 10px;
}

.action-left .el-button {
  padding: 6px 12px;
  min-width: auto;
  white-space: nowrap;
  flex-shrink: 0;
}

.table-info {
  font-size: 12px;
  color: #1890ff;
  white-space: nowrap;
  font-weight: 500;
}

.pending-count {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  padding: 2px 8px;
  color: #fa8c16;
}

.table-wrapper {
  flex: 1;
  min-height: 500px; /* 设置最小高度，确保表格可见 */
  margin: 0;
  padding: 0 0 80px 0; /* 底部预留80px给分页组件 */
  overflow: hidden; /* 让表格内部处理滚动 */
  position: relative;
  height: calc(100% - 80px); /* 为分页组件预留80px空间 */
  max-height: calc(100vh - 350px); /* 限制最大高度，确保分页可见 */
}

.table-loading {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  color: #606266;
  font-size: 14px;
}

.loading-info {
  margin-top: 8px;
  font-size: 12px;
  color: #909399;
}

.cell-content { 
  width: 100%; 
  height: 100%; 
  padding: 6px 10px; 
  box-sizing: border-box; 
}

.changed-cell { 
  background-color: #fdf6ec; 
}

.null-value { 
  color: #a8abb2; 
  font-style: italic; 
}

.new-row .el-table-v2__row-cell { 
  background-color: #f0f9eb !important; 
}

.new-row-cell {
  background-color: #f0f9eb !important;
  border: 1px dashed #67c23a;
}

.modified-row .el-table-v2__row-cell { 
  background-color: #fdf6ec !important; 
}

.id-field { 
  font-family: 'Consolas', 'Monaco', 'Menlo', monospace;
  word-break: break-all;
  font-size: 13px;
  color: #409EFF;
  white-space: normal;
  overflow: visible;
  line-height: 1.2;
}
</style>

<!-- 添加全局样式 -->
<style>
/* SQL确认弹窗的全局样式 */
.sql-confirm-dialog {
  width: 700px !important;
  max-width: 90vw;
}

.sql-confirm-dialog .el-message-box__header {
  padding: 15px 20px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.sql-confirm-dialog .el-message-box__title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.sql-confirm-dialog .el-message-box__content {
  padding: 20px;
}

.sql-confirm-dialog .el-message-box__btns {
  padding: 12px 20px;
  border-top: 1px solid #e4e7ed;
}

.sql-confirm-dialog .el-button--primary {
  background-color: #409EFF;
  border-color: #409EFF;
  padding: 10px 20px;
}

.sql-confirm-dialog .el-button--default {
  padding: 10px 20px;
}

/* 针对SQL高亮代码块的样式 */
.sql-block {
  border-radius: 4px;
  background: #f8f8f8;
  padding: 12px;
  margin-bottom: 15px;
  border: 1px solid #e0e0e0;
  overflow: auto;
  font-family: 'Courier New', monospace;
}

.sql-block pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
}

.sql-keyword {
  color: #409EFF;
  font-weight: bold;
}

.sql-string {
  color: #67C23A;
}

.sql-number {
  color: #E6A23C;
}

.sql-identifier {
  color: #F56C6C;
}
</style> 