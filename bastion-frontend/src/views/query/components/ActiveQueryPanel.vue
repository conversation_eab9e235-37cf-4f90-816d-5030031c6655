<template>
  <div class="query-panel">
    <div class="editor-container" :style="{ height: editorHeight + 'px' }">
      <div ref="cmEditor" class="codemirror-container"></div>
    </div>

    <!-- 添加可拖动分隔条 -->
    <div class="resizer" @mousedown="startResize"></div>

    <!-- 顶部工具栏 -->
    <div class="top-toolbar">
      <!-- 左侧编辑操作区 -->
      <div class="edit-actions">
        <!-- 非编辑模式下显示"开启编辑"按钮 - 只在查询结果页面显示 -->
        <el-button
          v-if="shouldShowEditButton"
          type="primary"
          size="small"
          @click="handleEditModeChange(true)"
          class="edit-mode-button"
        >
          <el-icon><Edit /></el-icon>
          开启编辑
        </el-button>

        <!-- 编辑模式下显示操作按钮组 - 只在查询结果页面显示 -->
        <template v-if="currentEditModeEnabled && isQueryResult() && ((tab.results && tab.results.length > 0) || (props.tab.resultTabs && props.tab.resultTabs.length > 0))">
          <el-button type="primary" size="small" @click="handleTopbarSubmitChanges">
            <el-icon><Check /></el-icon>
            提交修改
          </el-button>
          <el-button type="success" size="small" @click="handleTopbarAddRow">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
          <el-button type="danger" size="small" @click="handleTopbarDeleteSelected" :disabled="!hasSelectedRows">
            <el-icon><Delete /></el-icon>
            删除
          </el-button>
          <el-button type="info" size="small" @click="handleEditModeChange(false)">
            <el-icon><Close /></el-icon>
            取消编辑
          </el-button>
        </template>
      </div>

      <!-- 右侧数据源选择器 -->
      <div class="datasource-selectors">
        <el-select
          v-model="selectedDatasourceId"
          placeholder="选择数据源"
          @change="handleDatasourceChange"
          size="small"
        >
          <el-option
            v-for="db in databases"
            :key="db.id"
            :label="db.name"
            :value="db.id"
          />
        </el-select>

        <el-select
          v-model="selectedSchema"
          placeholder="选择 Schema"
          @change="handleSchemaChange"
          :disabled="!selectedDatasourceId"
          size="small"
        >
          <el-option
            v-for="schema in availableSchemas"
            :key="schema.value"
            :label="schema.label"
            :value="schema.value"
          />
        </el-select>
      </div>
    </div>

    <!-- 结果标签页导航，移动到SQL编辑器和结果内容之间 -->
    <div class="result-tabs-nav" v-if="shouldShowResultTabsNav">
      <el-tooltip
        v-for="(resultTab, index) in (props.tab.resultTabs || [])"
        :key="resultTab ? resultTab.id : ('temp-' + index)"
        :content="getTabTooltipContent(resultTab)"
        placement="top"
        :show-after="200"
        :disabled="!resultTab.sql"
      >
        <div
          class="tab-item"
          :class="{ active: activeResultTab === resultTab.id }"
          @click="activeResultTab = resultTab.id"
        >
          <!-- 根据标签页类型显示不同的图标 -->
          <el-icon v-if="resultTab.isEditSubmission"><Edit /></el-icon>
          <el-icon v-else><Grid /></el-icon>
          <!-- 根据标签页类型显示不同的标题 -->
          <span v-if="resultTab.isEditSubmission">编辑结果 {{ index + 1 }}</span>
          <span v-else>Result {{ index + 1 }}</span>
          <span class="close-icon" @click.stop="closeResultTabById(resultTab.id)">×</span>
        </div>
      </el-tooltip>
    </div>

    <!-- 结果展示区域 - 重新设计为数据库客户端风格 -->
    <template v-if="props.tab.executed">
      <div class="results-container">
        <!-- 查询结果展示区域 -->
        <template>
          <!-- 结果内容区域 -->
          <div class="result-content">
            <!-- 主结果标签页显示 -->
            <div
              v-if="activeResultTab === 'result'"
              class="result-panel"
            >
              <!-- 执行结果消息提示 -->
              <div v-if="props.tab.messages && !props.tab.error" class="execution-message">
                <el-alert
                  :title="props.tab.messages"
                  type="success"
                  show-icon
                  :closable="false"
                />
              </div>

              <!-- 错误提示 -->
              <div v-if="props.tab.error" class="error-message">
                <el-alert
                  title="查询执行失败"
                  type="error"
                  :description="formatErrorMessage(props.tab.error)"
                  show-icon
                  :closable="false"
                />
              </div>

              <!-- 表格组件 -->
              <EditableResultTable
                v-if="props.tab.results && props.tab.results.length > 0"
                :ref="(el) => { editableResultTableRef = el; }"
                :data="paginatedResults"
                :columns="normalizeColumns(props.tab.columns, props.tab.results)"
                :is-read-only="!(resultTabsEditMode['main'] || false)"
                :is-clickhouse="false"
                :tab-index="'main'"
                @cell-dblclick="handleCellDblClick"
                @update-cell-value="handleUpdateCellValue"
                @submit:changes="handleSubmitChanges"
                @toggle-edit-mode="handleToggleEditMode"
                @generate-backup-sql="handleGenerateBackupSql"
                @pending-changes-updated="handlePendingChangesUpdated"
                @selection-changed="(count) => handleSelectionChanged(count, 'main')"
              />

              <!-- 调试信息 -->
              <div v-else-if="props.tab.results" class="debug-info" style="padding: 20px; background: #f5f5f5; margin: 10px; border-radius: 4px;">
                <h4>主表格未显示 - 调试信息</h4>
                <p>props.tab.results存在: {{ !!props.tab.results }}</p>
                <p>props.tab.results长度: {{ props.tab.results?.length || 0 }}</p>
                <p>props.tab.columns: {{ JSON.stringify(props.tab.columns) }}</p>
                <p>normalizeColumns结果长度: {{ normalizeColumns(props.tab.columns, props.tab.results).length }}</p>
                <p>normalizeColumns结果: {{ JSON.stringify(normalizeColumns(props.tab.columns, props.tab.results)) }}</p>
              </div>

              <div v-else-if="!props.tab.error && !props.tab.messages" class="no-data-message">
                <el-empty description="查询未返回数据" />
              </div>
            </div>

            <!-- 额外结果标签页显示 -->
            <div
              v-for="(resultTab, index) in (props.tab.resultTabs || [])"
              :key="resultTab ? resultTab.id : ('temp-content-' + index)"
              v-show="activeResultTab === resultTab.id"
              class="result-panel"
            >
                <!-- 如果是手动编辑提交的结果，显示操作执行结果 -->
                <div v-if="resultTab.isEditSubmission && resultTab.multiDmlResults" class="edit-submission-results">
                  <div class="multi-sql-execution-layout">
                    <!-- 左侧：执行概览 -->
                    <div class="execution-summary-panel">
                      <div class="execution-summary">
                        <div class="summary-header">
                          <el-icon class="summary-icon" :style="{color: '#409EFF'}"><DataBoard /></el-icon>
                          <span class="summary-title">手动编辑提交结果</span>
                          <el-tag :type="getEditSubmissionSummaryType(resultTab.multiDmlResults)" size="small" class="summary-tag">
                            {{ getEditSubmissionSummaryText(resultTab.multiDmlResults) }}
                          </el-tag>
                        </div>
                        <div class="summary-stats">
                          <div class="stat-item">
                            <el-icon><Document /></el-icon>
                            <span>总计：{{ resultTab.multiDmlResults.length }} 项操作</span>
                          </div>
                          <div class="stat-item success">
                            <el-icon><CircleCheckFilled /></el-icon>
                            <span>成功：{{ resultTab.multiDmlResults.filter(r => r.success).length }} 项</span>
                          </div>
                          <div class="stat-item error" v-if="resultTab.multiDmlResults.filter(r => !r.success).length > 0">
                            <el-icon><CircleCloseFilled /></el-icon>
                            <span>失败：{{ resultTab.multiDmlResults.filter(r => !r.success).length }} 项</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 右侧：操作详情列表 -->
                    <div class="sql-results-panel">
                      <div class="sql-results-list">
                        <div
                          v-for="(item, idx) in resultTab.multiDmlResults"
                          :key="idx"
                          class="sql-result-item"
                          :class="{ 'success': item.success, 'error': !item.success }"
                        >
                          <div class="sql-index">
                            <el-icon class="status-icon" :style="{color: item.success ? '#67C23A' : '#F56C6C'}">
                              <CircleCheckFilled v-if="item.success" />
                              <CircleCloseFilled v-else />
                            </el-icon>
                            <span class="index-number">{{ idx + 1 }}</span>
                          </div>
                          <div class="sql-content">
                            <div class="sql-text">{{ item.sql }}</div>
                            <div class="result-info">
                              <span class="success-message" v-if="item.success">{{ getMultiSqlResultMessage(item) }}</span>
                              <span class="error-message" v-if="item.error">{{ formatErrorMessage(item.error) }}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 普通查询结果显示 -->
                <template v-else>
                  <!-- 执行结果消息提示 -->
                  <div v-if="resultTab.messages && !resultTab.error" class="execution-message">
                    <el-alert
                      :title="resultTab.messages"
                      type="success"
                      show-icon
                      :closable="false"
                    />
                  </div>

                  <!-- 错误提示 -->
                  <div v-if="resultTab.error" class="error-message">
                    <el-alert
                      title="查询执行失败"
                      type="error"
                      :description="formatErrorMessage(resultTab.error)"
                      show-icon
                      :closable="false"
                    />
                  </div>

                  <!-- 结果统计信息 -->
                  <!-- <div v-if="resultTab.results && resultTab.results.length > 0" class="result-stats">
                    <span class="record-count">
                      <el-icon><Document /></el-icon>
                      共 {{ resultTab.results.length }} 条记录，当前显示第 {{ (((resultTab.currentPage || 1) - 1) * (resultTab.pageSize || 20)) + 1 }}-{{ Math.min((resultTab.currentPage || 1) * (resultTab.pageSize || 20), resultTab.results.length) }} 条
                    </span>

                  </div> -->
                  
                  <!-- 表格组件 -->
                  <EditableResultTable
                    v-if="resultTab.results && resultTab.results.length > 0"
                    :ref="(el) => setTableRef(el, index)"
                    :data="getResultTabPaginatedData(resultTab)"
                    :columns="normalizeColumns(resultTab.columns, resultTab.results)"
                    :is-read-only="!(resultTabsEditMode[index] || false)"
                    :is-clickhouse="false"
                    :tab-index="index"
                    @cell-dblclick="handleCellDblClick"
                    @update-cell-value="handleUpdateCellValue"
                    @submit:changes="handleSubmitChanges"
                    @toggle-edit-mode="handleToggleEditMode"
                    @generate-backup-sql="handleGenerateBackupSql"
                    @pending-changes-updated="handlePendingChangesUpdated"
                    @selection-changed="(count) => handleSelectionChanged(count, index)"
                  />

                  <!-- 调试信息 -->
                  <div v-else class="debug-info" style="padding: 20px; background: #f5f5f5; margin: 10px; border-radius: 4px;">
                    <h4>表格未显示 - 调试信息</h4>
                    <p>resultTab.results存在: {{ !!resultTab.results }}</p>
                    <p>resultTab.results长度: {{ resultTab.results?.length || 0 }}</p>
                    <p>resultTab.columns: {{ JSON.stringify(resultTab.columns) }}</p>
                    <p>normalizeColumns结果长度: {{ normalizeColumns(resultTab.columns, resultTab.results).length }}</p>
                    <p>normalizeColumns结果: {{ JSON.stringify(normalizeColumns(resultTab.columns, resultTab.results)) }}</p>
                  </div>

                  <!-- 无数据提示 -->
                  <div v-if="!resultTab.error && !resultTab.messages && (!resultTab.results || resultTab.results.length === 0)" class="no-data-message">
                    <el-empty description="查询未返回数据" />
                  </div>
                </template>

                <!-- 分页组件已移到外层 -->
              </div>
          </div>
        </template>
      </div>
    </template>

   
    <!-- 执行中状态 - 增强显示效果 -->
    <div v-if="isExecuting" class="executing-container">
      <div class="executing-overlay">
        <div class="executing-content">
          <el-icon class="is-loading executing-icon"><Loading /></el-icon>
          <div class="executing-text">{{ getExecutingText() }}</div>
          <!-- <div class="executing-subtext">请稍候，查询结果将在此处显示</div> -->
          <div class="cancel-query-actions">
            <el-button type="danger" size="small" @click="handleCancelQuery">取消操作</el-button>
            <el-checkbox v-model="killQueryInDatabase" size="small">同时终止数据库中的操作进程</el-checkbox>
          </div>
        </div>
      </div>
    </div>

    <!-- 添加数据详情对话框 -->
    <el-dialog
      v-model="cellDetailVisible"
      :title="cellDetailTitle"
      width="600px"
      :close-on-click-modal="false"
      :append-to-body="true"
      :modal-append-to-body="false"
      :close-on-press-escape="true"
      :destroy-on-close="false"
      @closed="handleDetailDialogClosed"
      @open="onDialogOpen"
    >
      <!-- 根据是否处于编辑模式和是否可编辑状态显示不同内容 -->
      <div class="cell-detail-content" v-if="!isEditing && (cellDetailTitle.includes('查看') || !currentEditModeEnabled)">
        <pre>{{ cellDetailContent }}</pre>
      </div>
      <div class="cell-edit-content" v-else>
        <el-input
          v-model="cellEditValue"
          :type="getCellInputType()"
          :rows="5"
          :autosize="{ minRows: 3, maxRows: 10 }"
          placeholder="请输入新的值"
          ref="cellEditInputRef"
          autofocus
        />
      </div>
      <template #footer>
        <div v-if="!isEditing && (cellDetailTitle.includes('查看') || !currentEditModeEnabled)">
          <el-button type="primary" @click="copyCellContent">复制内容</el-button>
          <el-button 
            type="warning" 
            @click="startEditing" 
            v-if="currentEditModeEnabled"
          >编辑</el-button>
          <el-button @click="closeDetailDialog">关闭</el-button>
        </div>
        <div v-else>
          <el-button type="primary" @click="submitEdit">保存修改</el-button>
          <el-button @click="cancelEditing">取消</el-button>
        </div>
      </template>

      <!-- Add this section to display the backup SQL -->
      <div v-if="isEditing && backupSql" class="backup-sql-display">
        <strong>备份语句:</strong>
        <pre>{{ backupSql }}</pre>
      </div>
    </el-dialog>

    <!-- 添加编辑权限申请对话框 -->
    <el-dialog
      v-model="requestEditPermissionVisible"
      title="申请编辑权限"
      width="400px"
    >
      <div>
        <p>您当前没有编辑数据的权限，是否需要向管理员申请开通？</p>
        <el-input
          v-model="editPermissionReason"
          type="textarea"
          :rows="3"
          placeholder="请简要说明申请原因（可选）"
        />
      </div>
      <template #footer>
        <el-button type="primary" @click="submitEditPermissionRequest">提交申请</el-button>
        <el-button @click="requestEditPermissionVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- Backup SQL Dialog -->
    <el-dialog
      v-model="backupDialogVisible"
      title="备份SQL语句"
      width="60%"
      :append-to-body="true"
    >
      <p>这是为您生成的数据备份语句。请在继续执行前复制或保存。</p>
      <el-input
        v-model="backupSqlForDialog"
        type="textarea"
        :rows="15"
        readonly
        placeholder="备份SQL"
      />
      <template #footer>
        <el-button @click="copyBackupSql">复制SQL</el-button>
        <el-button type="primary" @click="executeAfterBackup">继续执行</el-button>
        <el-button @click="backupDialogVisible = false">取消</el-button>
      </template>
    </el-dialog>

    <!-- 独立的分页组件 - 固定在底部 -->
    <div
      v-if="hasActiveResultData"
      class="independent-pagination-container"
    >
      <el-pagination
        :current-page="currentActiveTabPage"
        :page-size="currentActiveTabPageSize"
        :page-sizes="[10, 20, 50, 100]"
        :total="currentActiveTabTotal"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleIndependentSizeChange"
        @current-change="handleIndependentPageChange"
      />
    </div>

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick, getCurrentInstance } from 'vue'
import { ElMessage, ElLoading, ElMessageBox } from 'element-plus'
import { CaretRight, Brush, Download, Plus, Document, Grid, Loading, Edit, Check, Delete, Close, CircleCheckFilled, WarningFilled, DataBoard, CircleCloseFilled } from '@element-plus/icons-vue'
import { EditorState, StateField, StateEffect } from '@codemirror/state'
import { EditorView, keymap, placeholder } from '@codemirror/view'
import { indentWithTab } from '@codemirror/commands'
import { sql } from '@codemirror/lang-sql'
import { autocompletion, completionKeymap, CompletionContext } from '@codemirror/autocomplete'
import { defaultKeymap, history, historyKeymap } from '@codemirror/commands'
import { syntaxHighlighting, HighlightStyle } from '@codemirror/language'
import { tags } from '@lezer/highlight'
import { oneDark } from '@codemirror/theme-one-dark'
import { getDatabaseTables, getTableColumns, addTableRow, submitBatchChanges } from '@/api/database'
import { getSchemaList } from '@/api/datasource'
import { getDatabaseFullMetadata } from '@/api/database'
// 导入EditableResultTable组件
import EditableResultTable from '../result/EditableResultTable.vue'
import { useBackupSqlGenerator } from '@/composables/useBackupSqlGenerator.js'
// 导入store和query store
import store from '@/store'
import { useQueryStore } from '@/store/query'

// 添加结果表引用集合 - 确保初始化为一个空数组
const resultTableRefs = ref([]);
// 立即检查并确保它是一个数组
if (!Array.isArray(resultTableRefs.value)) {
  console.warn('resultTableRefs.value不是数组，强制初始化为空数组');
  resultTableRefs.value = [];
}

// 引用EditableResultTable组件
const editableResultTableRef = ref(null);

// 安全地设置表格引用的函数
const setTableRef = (el, index) => {
  try {
    if (!el) return;

    // 获取当前循环的索引
    const currentIndex = index !== undefined ? index :
                        (el.$vnode && el.$vnode.key ?
                         parseInt(el.$vnode.key.toString().replace(/\D/g, '')) :
                         null);

    // 如果无法获取索引，尝试从当前上下文获取
    const contextIndex = getCurrentInstance()?.vnode?.key ||
                         getCurrentInstance()?.parent?.vnode?.key;

    const finalIndex = currentIndex !== null ? currentIndex :
                      (contextIndex ? parseInt(contextIndex.toString().replace(/\D/g, '')) : 0);

    // 确保resultTableRefs.value是一个数组
    if (!resultTableRefs.value || !Array.isArray(resultTableRefs.value)) {
      resultTableRefs.value = [];
    }

    // 确保数组有足够长度
    while (resultTableRefs.value.length <= finalIndex) {
      resultTableRefs.value.push(undefined);
    }

    // 设置引用
    resultTableRefs.value[finalIndex] = el;

    // 同时设置editableResultTableRef，确保至少有一个引用可用
    if (!editableResultTableRef.value) {
      editableResultTableRef.value = el;
    }

    // 如果是当前活动标签页，也设置为editableResultTableRef
    // 通过索引找到对应的标签页ID
    if (activeResultTab && props.tab.resultTabs && finalIndex < props.tab.resultTabs.length) {
      const tabId = props.tab.resultTabs[finalIndex].id;
      if (activeResultTab.value === tabId) {
        editableResultTableRef.value = el;
      }
    }
  } catch (err) {
    console.error('设置表格引用时出错:', err);
  }
};

// 控制主结果标签页的显示
// 默认不显示主结果标签页，改为使用独立的结果标签页
const showMainResultTab = ref(true);

// 获取执行概览类型
const getExecutionSummaryType = () => {
  if (!props.tab.multiDmlResults || props.tab.multiDmlResults.length === 0) return 'info';
  const failedCount = props.tab.multiDmlResults.filter(r => !r.success).length;
  if (failedCount === 0) return 'success';
  if (failedCount === props.tab.multiDmlResults.length) return 'danger';
  return 'warning';
};

// 获取执行概览文本
const getExecutionSummaryText = () => {
  if (!props.tab.multiDmlResults || props.tab.multiDmlResults.length === 0) return '无结果';
  const successCount = props.tab.multiDmlResults.filter(r => r.success).length;
  const failedCount = props.tab.multiDmlResults.filter(r => !r.success).length;

  if (failedCount === 0) return '全部成功';
  if (successCount === 0) return '全部失败';
  return `部分成功 (${successCount}/${props.tab.multiDmlResults.length})`;
};

// 获取手动编辑提交结果的概览类型
const getEditSubmissionSummaryType = (multiDmlResults) => {
  if (!multiDmlResults || multiDmlResults.length === 0) return 'info';
  const failedCount = multiDmlResults.filter(r => !r.success).length;
  if (failedCount === 0) return 'success';
  if (failedCount === multiDmlResults.length) return 'danger';
  return 'warning';
};

// 获取手动编辑提交结果的概览文本
const getEditSubmissionSummaryText = (multiDmlResults) => {
  if (!multiDmlResults || multiDmlResults.length === 0) return '无结果';
  const successCount = multiDmlResults.filter(r => r.success).length;
  const failedCount = multiDmlResults.filter(r => !r.success).length;

  if (failedCount === 0) return '全部成功';
  if (successCount === 0) return '全部失败';
  return `部分成功 (${successCount}/${multiDmlResults.length})`;
};

// 判断是否为DDL或其他操作（非SELECT、非DML）
const isDDLOrOtherOperation = (resultTab) => {
  // 如果有结果数据且列不是affected_rows，说明是SELECT查询
  if (resultTab.results && resultTab.results.length > 0 &&
      resultTab.columns && resultTab.columns.length > 0 &&
      resultTab.columns[0] !== 'affected_rows') {
    return false;
  }

  // 如果明确标记为DML结果，也不是DDL
  if (resultTab.isDmlResult) {
    return false;
  }

  // 如果有SQL语句，通过SQL判断
  if (resultTab.sql) {
    const sqlLower = resultTab.sql.toLowerCase().trim();
    return sqlLower.startsWith('create ') ||
           sqlLower.startsWith('alter ') ||
           sqlLower.startsWith('drop ') ||
           sqlLower.startsWith('truncate ') ||
           sqlLower.startsWith('rename ') ||
           sqlLower.startsWith('desc ') ||
           sqlLower.startsWith('describe ') ||
           sqlLower.startsWith('show ') ||
           sqlLower.startsWith('explain ') ||
           sqlLower.startsWith('use ') ||
           sqlLower.startsWith('set ');
  }

  // 如果没有结果数据，且不是DML，可能是DDL或其他操作
  return (!resultTab.results || resultTab.results.length === 0) && !resultTab.isDmlResult;
};

// 获取DDL操作的标题
const getDDLOperationTitle = (resultTab) => {
  if (!resultTab.sql) return '执行成功';

  const sqlLower = resultTab.sql.toLowerCase().trim();

  if (sqlLower.startsWith('create ')) {
    if (sqlLower.includes('table')) return '表创建成功';
    if (sqlLower.includes('database') || sqlLower.includes('schema')) return '数据库创建成功';
    if (sqlLower.includes('index')) return '索引创建成功';
    if (sqlLower.includes('view')) return '视图创建成功';
    return '对象创建成功';
  }

  if (sqlLower.startsWith('alter ')) {
    if (sqlLower.includes('table')) return '表结构修改成功';
    if (sqlLower.includes('database') || sqlLower.includes('schema')) return '数据库修改成功';
    return '对象修改成功';
  }

  if (sqlLower.startsWith('drop ')) {
    if (sqlLower.includes('table')) return '表删除成功';
    if (sqlLower.includes('database') || sqlLower.includes('schema')) return '数据库删除成功';
    if (sqlLower.includes('index')) return '索引删除成功';
    if (sqlLower.includes('view')) return '视图删除成功';
    return '对象删除成功';
  }

  if (sqlLower.startsWith('truncate ')) return '表清空成功';
  if (sqlLower.startsWith('rename ')) return '重命名成功';
  // DESC、SHOW、EXPLAIN等查询语句应该显示表格数据，不应该显示操作消息
  if (sqlLower.startsWith('use ')) return '数据库切换成功';
  if (sqlLower.startsWith('set ')) return '参数设置成功';

  return '执行成功';
};

// 获取DDL操作的消息
const getDDLOperationMessage = (resultTab) => {
  // 优先使用自定义消息
  if (resultTab.messages) {
    return resultTab.messages;
  }

  if (!resultTab.sql) return '操作已完成';

  const sqlLower = resultTab.sql.toLowerCase().trim();

  // DESC、SHOW、EXPLAIN等查询语句应该显示表格数据，不应该显示操作消息
  // 这些语句的处理已经移到 isSelectQuery 函数中

  if (sqlLower.startsWith('create ')) {
    return '对象已成功创建';
  }

  if (sqlLower.startsWith('alter ')) {
    return '对象已成功修改';
  }

  if (sqlLower.startsWith('drop ')) {
    return '对象已成功删除';
  }

  if (sqlLower.startsWith('truncate ')) {
    return '表数据已清空';
  }

  if (sqlLower.startsWith('rename ')) {
    return '对象已成功重命名';
  }

  if (sqlLower.startsWith('use ')) {
    return '已切换到指定数据库';
  }

  if (sqlLower.startsWith('set ')) {
    return '参数已成功设置';
  }

  return '操作已成功完成';
};

// 获取多SQL执行结果的消息
const getMultiSqlResultMessage = (item) => {
  if (!item.success) return '';

  const sql = item.sql?.toLowerCase().trim() || '';

  // SELECT查询
  if (sql.startsWith('select')) {
    const rowCount = item.affectedRows || 0;
    return `查询成功，返回 ${rowCount} 行数据`;
  }

  // INSERT操作
  if (sql.startsWith('insert')) {
    const affectedRows = item.affectedRows || 0;
    return `插入成功，${affectedRows} 行受影响`;
  }

  // UPDATE操作
  if (sql.startsWith('update')) {
    const affectedRows = item.affectedRows || 0;
    return `更新成功，${affectedRows} 行受影响`;
  }

  // DELETE操作
  if (sql.startsWith('delete')) {
    const affectedRows = item.affectedRows || 0;
    return `删除成功，${affectedRows} 行受影响`;
  }

  // CREATE操作
  if (sql.startsWith('create')) {
    if (sql.includes('table')) return '表创建成功';
    if (sql.includes('database') || sql.includes('schema')) return '数据库创建成功';
    if (sql.includes('index')) return '索引创建成功';
    if (sql.includes('view')) return '视图创建成功';
    return '对象创建成功';
  }

  // ALTER操作
  if (sql.startsWith('alter')) {
    if (sql.includes('table')) return '表结构修改成功';
    if (sql.includes('database') || sql.includes('schema')) return '数据库修改成功';
    return '对象修改成功';
  }

  // DROP操作
  if (sql.startsWith('drop')) {
    if (sql.includes('table')) return '表删除成功';
    if (sql.includes('database') || sql.includes('schema')) return '数据库删除成功';
    if (sql.includes('index')) return '索引删除成功';
    if (sql.includes('view')) return '视图删除成功';
    return '对象删除成功';
  }

  // TRUNCATE操作
  if (sql.startsWith('truncate')) {
    return '表清空成功';
  }

  // RENAME操作
  if (sql.startsWith('rename')) {
    return '重命名成功';
  }

  // DESC、SHOW、EXPLAIN等查询语句应该显示表格数据，不应该显示操作消息

  // USE操作
  if (sql.startsWith('use')) {
    return '数据库切换成功';
  }

  // SET操作
  if (sql.startsWith('set')) {
    return '参数设置成功';
  }

  // 默认情况，显示影响行数
  const affectedRows = item.affectedRows || 0;
  return `执行成功，${affectedRows} 行受影响`;
};

// 计算是否应该显示结果标签页导航
const shouldShowResultTabsNav = computed(() => {
  const resultTabs = props.tab.resultTabs || [];

  // 只要有结果标签页就显示导航
  return resultTabs.length > 0;
});

// 获取标签页tooltip内容
const getTabTooltipContent = (resultTab) => {
  if (!resultTab || !resultTab.sql) {
    return '';
  }

  const sql = resultTab.sql.trim();

  // 检查是否是SELECT语句
  if (sql.toLowerCase().startsWith('select')) {
    // 对于SELECT语句，显示完整SQL（可能需要截断）
    return sql.length > 100 ? sql.substring(0, 100) + '...' : sql;
  }

  // 对于变更语句（UPDATE、INSERT、DELETE等），显示简化信息
  if (sql.toLowerCase().startsWith('update')) {
    // 提取表名
    const match = sql.match(/update\s+(\w+)/i);
    const tableName = match ? match[1] : '表';
    return `UPDATE ${tableName} - 更新数据`;
  }

  if (sql.toLowerCase().startsWith('insert')) {
    const match = sql.match(/insert\s+into\s+(\w+)/i);
    const tableName = match ? match[1] : '表';
    return `INSERT INTO ${tableName} - 插入数据`;
  }

  if (sql.toLowerCase().startsWith('delete')) {
    const match = sql.match(/delete\s+from\s+(\w+)/i);
    const tableName = match ? match[1] : '表';
    return `DELETE FROM ${tableName} - 删除数据`;
  }

  // 对于其他类型的SQL，显示前50个字符
  return sql.length > 50 ? sql.substring(0, 50) + '...' : sql;
};

// 获取执行中的文本
const getExecutingText = () => {
  const sql = sqlContent.value.trim().toLowerCase();

  if (sql.startsWith('select')) {
    return '正在执行查询...';
  } else if (sql.startsWith('update')) {
    return '正在执行更新...';
  } else if (sql.startsWith('insert')) {
    return '正在执行插入...';
  } else if (sql.startsWith('delete')) {
    return '正在执行删除...';
  } else if (sql.startsWith('create')) {
    return '正在创建对象...';
  } else if (sql.startsWith('drop')) {
    return '正在删除对象...';
  } else if (sql.startsWith('alter')) {
    return '正在修改对象...';
  } else {
    return '正在执行操作...';
  }
};

// 判断当前是否为查询结果（而不是变更操作结果）
const isQueryResult = () => {
  console.log('[DEBUG] isQueryResult 检查开始');
  console.log('[DEBUG] activeResultTab.value:', activeResultTab.value);

  // 处理主结果标签页的情况
  if (activeResultTab.value === 'result') {
    console.log('[DEBUG] 检查主结果标签页');
    const sql = props.tab.sql || sqlContent.value;
    if (sql) {
      const result = isSelectQuery(sql);
      console.log('[DEBUG] 主结果标签页SQL:', sql);
      console.log('[DEBUG] 主结果标签页 isSelectQuery 结果:', result);
      return result;
    }
    // 如果没有SQL，检查是否有表格数据
    if (props.tab.columns && Array.isArray(props.tab.columns) && props.tab.columns.length > 0) {
      console.log('[DEBUG] 主结果标签页有列数据');
      return true; // 有列数据，认为是查询结果
    }
    console.log('[DEBUG] 主结果标签页无SQL和列数据，返回false');
    return false;
  }

  // 处理额外结果标签页的情况
  const targetTab = props.tab.resultTabs?.find(tab => tab.id === activeResultTab.value);

  if (targetTab) {
    console.log('[DEBUG] 找到目标标签页:', targetTab);

    // 优先检查isSelect属性（最可靠）
    if (targetTab.hasOwnProperty('isSelect')) {
      console.log('[DEBUG] 标签页有 isSelect 属性:', targetTab.isSelect);
      return targetTab.isSelect === true;
    }

    // 检查标签页的SQL是否为查询类型（SELECT、SHOW、DESC等）
    if (targetTab.sql) {
      const result = isSelectQuery(targetTab.sql);
      console.log('[DEBUG] 标签页SQL:', targetTab.sql);
      console.log('[DEBUG] isSelectQuery 结果:', result);
      return result;
    }

    // 如果没有SQL，检查是否有表格数据（columns存在且不是DML结果）
    if (targetTab.columns && Array.isArray(targetTab.columns) && targetTab.columns.length > 0) {
      const isDml = targetTab.isDmlResult === true;
      console.log('[DEBUG] 标签页有列数据，isDmlResult:', isDml);
      return !isDml; // 有列数据且不是DML结果，则认为是查询结果
    }
  } else {
    console.log('[DEBUG] 未找到目标标签页，activeResultTab:', activeResultTab.value);
    console.log('[DEBUG] 可用的标签页:', props.tab.resultTabs?.map(tab => ({ id: tab.id, sql: tab.sql?.substring(0, 50) })));
  }

  // 默认返回false（不显示编辑按钮）
  console.log('[DEBUG] isQueryResult 返回 false（默认）');
  return false;
};

// 计算属性：是否应该显示编辑按钮
const shouldShowEditButton = computed(() => {
  const canEnable = canEnableEditMode.value;
  const notCurrentlyEditing = !currentEditModeEnabled.value;
  const isQuery = isQueryResult();
  const noError = !hasCurrentTabError();
  // 检查当前活动标签页是否有结果数据
  let hasResults = false;
  if (activeResultTab.value === 'result') {
    // 主结果标签页
    hasResults = props.tab.results && props.tab.results.length > 0;
  } else {
    // 额外结果标签页
    const targetTab = props.tab.resultTabs?.find(tab => tab.id === activeResultTab.value);
    hasResults = targetTab && targetTab.results && targetTab.results.length > 0;
  }

  console.log('[DEBUG] 编辑按钮显示条件检查:');
  console.log('[DEBUG] activeResultTab:', activeResultTab.value);
  console.log('[DEBUG] props.isAdmin:', props.isAdmin);
  console.log('[DEBUG] props.userPermissions:', props.userPermissions);
  console.log('[DEBUG] canEnableEditMode:', canEnable);
  console.log('[DEBUG] !currentEditModeEnabled:', notCurrentlyEditing);
  console.log('[DEBUG] isQueryResult():', isQuery);
  console.log('[DEBUG] !hasCurrentTabError():', noError);
  console.log('[DEBUG] hasResults:', hasResults);

  // 打印当前标签页的调试信息
  if (activeResultTab.value === 'result') {
    console.log('[DEBUG] 主结果标签页数据:');
    console.log('[DEBUG] 主结果标签页sql:', props.tab.sql);
    console.log('[DEBUG] 主结果标签页results长度:', props.tab.results?.length || 0);
    console.log('[DEBUG] 主结果标签页columns长度:', props.tab.columns?.length || 0);
    console.log('[DEBUG] 主结果标签页error:', props.tab.error);
  } else {
    const targetTab = props.tab.resultTabs?.find(tab => tab.id === activeResultTab.value);
    console.log('[DEBUG] 额外结果标签页数据:', targetTab);
    if (targetTab) {
      console.log('[DEBUG] 标签页sql:', targetTab.sql);
      console.log('[DEBUG] 标签页results长度:', targetTab.results?.length || 0);
      console.log('[DEBUG] 标签页columns长度:', targetTab.columns?.length || 0);
      console.log('[DEBUG] 标签页error:', targetTab.error);
      console.log('[DEBUG] 标签页isDmlResult:', targetTab.isDmlResult);
      console.log('[DEBUG] 标签页isSelect:', targetTab.isSelect);
    } else {
      console.log('[DEBUG] 未找到当前活动标签页的数据！');
      console.log('[DEBUG] 所有可用标签页:', props.tab.resultTabs?.map(tab => ({ id: tab.id, sql: tab.sql?.substring(0, 50) })));
    }
  }

  const shouldShow = canEnable && notCurrentlyEditing && isQuery && noError && hasResults;
  console.log('[DEBUG] 最终是否显示编辑按钮:', shouldShow);

  return shouldShow;
});

// 判断SQL是否为查询语句（返回表格数据的语句）
const isSelectQuery = (sql) => {
  console.log('[DEBUG] isSelectQuery 检查SQL:', sql);
  if (!sql) {
    console.log('[DEBUG] SQL为空，返回false');
    return false;
  }

  // 清理SQL：去除前后空白和注释
  const cleanSql = sql.trim().toLowerCase();
  console.log('[DEBUG] 清理后的SQL:', cleanSql);

  // 所有返回表格数据的查询类型
  const queryKeywords = [
    'select',    // SELECT查询
    'desc',      // DESC表结构
    'describe',  // DESCRIBE表结构
    'show',      // SHOW语句
    'explain',   // EXPLAIN执行计划
    'with'       // WITH子句（CTE）
  ];

  const result = queryKeywords.some(keyword =>
    cleanSql.startsWith(keyword + ' ') || cleanSql === keyword
  );

  console.log('[DEBUG] isSelectQuery 结果:', result);
  return result;
};



// 检查当前标签页是否有错误
const hasCurrentTabError = () => {
  // 处理主结果标签页的情况
  if (activeResultTab.value === 'result') {
    return !!props.tab.error;
  }

  // 处理额外结果标签页的情况
  const targetTab = props.tab.resultTabs?.find(tab => tab.id === activeResultTab.value);
  return targetTab ? !!targetTab.error : false;
};

// 添加一个辅助函数，用于获取当前活动的表格引用
const getCurrentTableRef = () => {
  try {
    // 如果activeResultTab不存在，直接返回null
    if (!activeResultTab || !activeResultTab.value) {
      return null;
    }

    // 如果是额外结果标签页，优先从resultTableRefs中获取
    if (activeResultTab.value.startsWith('result-tab-')) {
      try {
        // 通过标签页ID找到对应的索引
        const tabIndex = props.tab.resultTabs ? props.tab.resultTabs.findIndex(tab => tab.id === activeResultTab.value) : -1;

        if (tabIndex >= 0 && resultTableRefs.value && tabIndex < resultTableRefs.value.length && resultTableRefs.value[tabIndex]) {
          return resultTableRefs.value[tabIndex];
        }
      } catch (err) {
        console.error('解析result-tab-索引时出错:', err);
      }
    }

    // 如果是主结果标签页或找不到对应的表格引用，使用主表格引用
    if (activeResultTab.value === 'result' || !activeResultTab.value.startsWith('result-tab-')) {
      if (editableResultTableRef && editableResultTableRef.value) {
        return editableResultTableRef.value;
      }
    }

    // 如果resultTableRefs不存在或不是数组，返回null
    if (!resultTableRefs || !resultTableRefs.value || !Array.isArray(resultTableRefs.value)) {
      return null;
    }

    // 如果是简化的格式 'result-1'，也尝试处理
    if (activeResultTab.value.startsWith('result-') && !activeResultTab.value.startsWith('result-tab-')) {
      try {
        const index = parseInt(activeResultTab.value.replace('result-', '')) - 1;

        if (index >= 0 && index < resultTableRefs.value.length && resultTableRefs.value[index]) {
          return resultTableRefs.value[index];
        }
      } catch (err) {
        console.error('解析result-索引时出错:', err);
      }
    }

    // 最后尝试遍历所有resultTableRefs找到第一个非空引用
    for (let i = 0; i < resultTableRefs.value.length; i++) {
      if (resultTableRefs.value[i]) {
        return resultTableRefs.value[i];
      }
    }

    // 如果都找不到，返回null
    return null;
  } catch (error) {
    console.error('获取表格引用时出错:', error);
    return null;
  }
};

const props = defineProps({
  tab: {
    type: Object,
    required: true
  },
  databases: {
    type: Array,
    required: true
  },
  // 接收从父组件传递的缓存
  autocompleteCache: {
    type: Object,
    default: () => ({})
  },
  isAdmin: {
    type: Boolean,
    default: false
  },
  // 新增：接收执行状态
  isQueryExecuting: {
    type: Boolean,
    default: false
  },
  // 新增：接收用户权限信息
  userPermissions: {
    type: Object,
    default: () => ({
      canEdit: false,
      canRequestEdit: true
    })
  }
})

const emit = defineEmits([
  'execute-query',
  'format-sql',
  'tab-database-change',
  'editor-ready',
  'update:sql',
  'cell-dblclick',
  'delete-row',
  'save-query',
  'execution-state-change', // 新增：执行状态变更事件
  'update:tab', // 新增：直接更新tab对象
  'refresh-autocomplete-cache', // 新增：刷新缓存事件
  'update-cell-value', // 新增：更新单元格值事件
  'request-edit-permission', // 新增：请求编辑权限事件
  'add-row', // 新增：添加行事件
  'submit:changes', // 新增批量提交事件
  'cancel-query' // 新增：取消操作事件
])

const sqlContent = ref(props.tab.sql || '')
// 将isExecuting改为计算属性，从标签页状态中读取
const isExecuting = computed(() => props.tab.isExecuting || false)
let queryStartTime = 0; // 用于记录查询开始时间
const selectedDatasourceId = ref(props.tab.datasourceId)
const selectedSchema = ref(props.tab.schema)
const currentPage = ref(1)
// 默认每页显示20条记录
const pageSize = ref(20)
const editorHeight = ref(300)
const outputContentRef = ref(null)
const activeResultTab = ref('result') // 默认显示主结果标签页

// 监听 props.tab.resultTabs 的变化，确保 activeResultTab 指向有效的标签页

// 标准化列格式，确保 EditableResultTable 能正确渲染
const normalizeColumns = (columns, results) => {
  console.log('[normalizeColumns] 输入参数:', { columns, resultsLength: results?.length });
  
  // 如果 columns 已经是正确的格式（对象数组），直接返回
  if (Array.isArray(columns) && columns.length > 0 && typeof columns[0] === 'object' && columns[0].prop) {
    console.log('[normalizeColumns] 已经是正确格式，直接返回:', columns);
    return columns;
  }
  
  // 如果 columns 是字符串数组，转换为对象数组
  if (Array.isArray(columns) && columns.length > 0 && typeof columns[0] === 'string') {
    const normalized = columns.map(col => ({ prop: col, label: col }));
    console.log('[normalizeColumns] 字符串数组转换为对象数组:', normalized);
    return normalized;
  }
  
  // 如果 columns 为空或无效，但有 results 数据，从第一行数据推断列名
  if (Array.isArray(results) && results.length > 0 && results[0]) {
    const firstRow = results[0];
    if (typeof firstRow === 'object') {
      const inferred = Object.keys(firstRow).map(key => ({ prop: key, label: key }));
      console.log('[normalizeColumns] 从结果数据推断列名:', inferred);
      return inferred;
    }
  }
  
  console.log('[normalizeColumns] 无法标准化，返回空数组');
  // 默认返回空数组
  return [];
};
watch(() => props.tab.resultTabs, (newResultTabs) => {
  console.log('[DEBUG] resultTabs 变化:', newResultTabs);
  if (newResultTabs && newResultTabs.length > 0) {
    // 如果当前活动标签页不存在于新的结果标签页列表中，需要切换
    const currentTabExists = newResultTabs.some(tab => tab.id === activeResultTab.value);
    if (!currentTabExists) {
      // 优先选择最后一个SELECT查询的结果标签页
      let targetTab = null;

      // 从后往前查找第一个SELECT查询的结果标签页
      for (let i = newResultTabs.length - 1; i >= 0; i--) {
        const tab = newResultTabs[i];
        if (tab.isSelect === true || (tab.sql && isSelectQuery(tab.sql))) {
          targetTab = tab;
          break;
        }
      }

      // 如果没找到SELECT查询，使用最后一个标签页
      if (!targetTab) {
        targetTab = newResultTabs[newResultTabs.length - 1];
      }

      console.log('[DEBUG] 当前活动标签页不存在，切换到目标标签页:', targetTab.id);
      activeResultTab.value = targetTab.id;
    }
  } else {
    console.log('[DEBUG] 没有结果标签页，切换到主结果标签页');
    activeResultTab.value = 'result';
  }
}, { immediate: true, deep: true });
const isMultiStatement = ref(false) // 新增：多语句执行标志
const resultTabs = computed(() => {
  // 如果tab中有resultTabs属性，使用它
  if (props.tab && props.tab.resultTabs && Array.isArray(props.tab.resultTabs)) {
    return props.tab.resultTabs;
  }
  // 否则使用空数组
  return [];
}); // 存储多SQL语句的结果标签页

const { generateInsertSql, generateUpdateSql } = useBackupSqlGenerator();

// State for the new backup dialog
const backupDialogVisible = ref(false);
const backupSqlForDialog = ref('');
const originalSqlToExecute = ref('');

// 计算属性：是否有实际查询结果数据
const hasResultData = computed(() => {
  const hasData = props.tab &&
         props.tab.results &&
         Array.isArray(props.tab.results) &&
         props.tab.results.length > 0 &&
         props.tab.columns &&
         Array.isArray(props.tab.columns) &&
         props.tab.columns.length > 0;
  return hasData;
})

// 确保每个标签页有自己的执行日志
const executionLogs = computed(() => {
  // 如果标签页没有executionLogs属性，初始化为空数组
  if (!props.tab.executionLogs) {
    props.tab.executionLogs = [];
  }
  return props.tab.executionLogs;
})

// 计算当前活动标签页的编辑模式状态
const currentEditModeEnabled = computed(() => {
  // 如果是主结果标签页
  if (activeResultTab.value === 'result') {
    const editMode = resultTabsEditMode.value['main'] || false;
    return editMode;
  }
  // 如果是额外结果标签页，通过ID在数组中查找实际索引
  else if (activeResultTab.value.startsWith('result-tab-') || activeResultTab.value.startsWith('result-')) {
    // 在 resultTabs 数组中查找当前活动标签页的索引
    const resultTabsArray = props.tab.resultTabs || [];
    const index = resultTabsArray.findIndex(tab => tab.id === activeResultTab.value);

    if (index !== -1) {
      const editMode = resultTabsEditMode.value[index] || false;
      return editMode;
    } else {
      return false;
    }
  }
  // 默认返回false
  return false;
})

// 新增编辑模式相关变量
const resultTabsEditMode = ref({
  'main': false, // 主结果标签页(Result 1)的编辑模式状态
}) // 存储所有结果标签页的编辑模式状态
const isEditing = ref(false)
const cellEditValue = ref('')
const currentEditingCell = ref(null)
const requestEditPermissionVisible = ref(false)
const editPermissionReason = ref('')

// 是否有选中的行
// 创建一个响应式的选择状态
const selectedRowsCount = ref(0);

// 监听表格选择状态变化
const updateSelectedRowsCount = () => {
  const tableRef = getCurrentTableRef();
  if (tableRef && tableRef.selectedRowKeys) {
    selectedRowsCount.value = tableRef.selectedRowKeys.size;
  } else {
    selectedRowsCount.value = 0;
  }
};

const hasSelectedRows = computed(() => {
  return selectedRowsCount.value > 0;
})

// 处理选择状态变化，只更新当前活动标签页的选择状态
const handleSelectionChanged = (count, tabIndex) => {
  // 如果是主结果标签页
  if (tabIndex === 'main' && activeResultTab.value === 'result') {
    selectedRowsCount.value = count;
    return;
  }

  // 检查这个表格是否是当前活动的标签页
  if (props.tab.resultTabs && props.tab.resultTabs[tabIndex]) {
    const tabId = props.tab.resultTabs[tabIndex].id;
    if (activeResultTab.value === tabId) {
      selectedRowsCount.value = count;
    }
  }
}

// 取消操作相关状态
const killQueryInDatabase = ref(true);

// 单元格详情状态变量
const cellDetailVisible = ref(false)
const cellDetailContent = ref('')
const cellDetailTitle = ref('')
const editingCellContext = ref(null) // 添加用于存储正在编辑的单元格信息

// 添加编辑器引用
const cmEditor = ref(null)
let editorView = null

// SQL关键字补全列表
const sqlKeywords = [
  // 基本关键字
  { label: 'SELECT', type: 'keyword' },
  { label: 'FROM', type: 'keyword' },
  { label: 'WHERE', type: 'keyword' },
  { label: 'INSERT', type: 'keyword' },
  { label: 'UPDATE', type: 'keyword' },
  { label: 'DELETE', type: 'keyword' },
  { label: 'GROUP BY', type: 'keyword' },
  { label: 'ORDER BY', type: 'keyword' },
  { label: 'HAVING', type: 'keyword' },
  { label: 'LIMIT', type: 'keyword' },
  { label: 'OFFSET', type: 'keyword' },
  
  // 连接类关键字
  { label: 'JOIN', type: 'keyword' },
  { label: 'LEFT JOIN', type: 'keyword' },
  { label: 'RIGHT JOIN', type: 'keyword' },
  { label: 'INNER JOIN', type: 'keyword' },
  { label: 'OUTER JOIN', type: 'keyword' },
  { label: 'FULL JOIN', type: 'keyword' },
  { label: 'CROSS JOIN', type: 'keyword' },
  { label: 'NATURAL JOIN', type: 'keyword' },
  
  // 表操作关键字
  { label: 'CREATE TABLE', type: 'keyword' },
  { label: 'ALTER TABLE', type: 'keyword' },
  { label: 'DROP TABLE', type: 'keyword' },
  { label: 'TRUNCATE TABLE', type: 'keyword' },
  { label: 'RENAME TABLE', type: 'keyword' },
  { label: 'SHOW CREATE TABLE', type: 'keyword' },
  
  // 数据库操作关键字
  { label: 'CREATE DATABASE', type: 'keyword' },
  { label: 'DROP DATABASE', type: 'keyword' },
  { label: 'USE', type: 'keyword' },
  
  // 视图操作关键字
  { label: 'CREATE VIEW', type: 'keyword' },
  { label: 'DROP VIEW', type: 'keyword' },
  { label: 'ALTER VIEW', type: 'keyword' },
  
  // 索引操作关键字
  { label: 'CREATE INDEX', type: 'keyword' },
  { label: 'DROP INDEX', type: 'keyword' },
  
  // 事务控制关键字
  { label: 'BEGIN', type: 'keyword' },
  { label: 'START TRANSACTION', type: 'keyword' },
  { label: 'COMMIT', type: 'keyword' },
  { label: 'ROLLBACK', type: 'keyword' },
  { label: 'SAVEPOINT', type: 'keyword' },
  
  // 数据控制关键字
  { label: 'GRANT', type: 'keyword' },
  { label: 'REVOKE', type: 'keyword' },
  
  // 子查询和公用表表达式
  { label: 'WITH', type: 'keyword' },
  { label: 'EXISTS', type: 'keyword' },
  { label: 'NOT EXISTS', type: 'keyword' },
  { label: 'ALL', type: 'keyword' },
  { label: 'ANY', type: 'keyword' },
  { label: 'SOME', type: 'keyword' },
  { label: 'UNION', type: 'keyword' },
  { label: 'UNION ALL', type: 'keyword' },
  { label: 'INTERSECT', type: 'keyword' },
  { label: 'EXCEPT', type: 'keyword' },
  { label: 'MINUS', type: 'keyword' },
  
  // 条件表达式关键字
  { label: 'CASE', type: 'keyword' },
  { label: 'WHEN', type: 'keyword' },
  { label: 'THEN', type: 'keyword' },
  { label: 'ELSE', type: 'keyword' },
  { label: 'END', type: 'keyword' },
  { label: 'NULLIF', type: 'keyword' },
  { label: 'COALESCE', type: 'keyword' },
  { label: 'add', type: 'keyword' },
  
  // 算术和比较运算符
  { label: 'AND', type: 'operator' },
  { label: 'OR', type: 'operator' },
  { label: 'NOT', type: 'operator' },
  { label: 'IN', type: 'operator' },
  { label: 'NOT IN', type: 'operator' },
  { label: 'BETWEEN', type: 'operator' },
  { label: 'NOT BETWEEN', type: 'operator' },
  { label: 'LIKE', type: 'operator' },
  { label: 'NOT LIKE', type: 'operator' },
  { label: 'ILIKE', type: 'operator' },
  { label: 'SIMILAR TO', type: 'operator' },
  { label: 'IS NULL', type: 'operator' },
  { label: 'IS NOT NULL', type: 'operator' },
  { label: 'IS DISTINCT FROM', type: 'operator' },
  { label: 'IS NOT DISTINCT FROM', type: 'operator' },
  
  // 常用函数
  { label: 'COUNT', type: 'function' },
  { label: 'SUM', type: 'function' },
  { label: 'AVG', type: 'function' },
  { label: 'MAX', type: 'function' },
  { label: 'MIN', type: 'function' },
  { label: 'DISTINCT', type: 'function' },
  { label: 'FIRST', type: 'function' },
  { label: 'LAST', type: 'function' },
  
  // 字符串函数
  { label: 'CONCAT', type: 'function' },
  { label: 'SUBSTRING', type: 'function' },
  { label: 'TRIM', type: 'function' },
  { label: 'UPPER', type: 'function' },
  { label: 'LOWER', type: 'function' },
  { label: 'LENGTH', type: 'function' },
  { label: 'REPLACE', type: 'function' },
  
  // 日期时间函数
  { label: 'NOW', type: 'function' },
  { label: 'CURRENT_DATE', type: 'function' },
  { label: 'CURRENT_TIME', type: 'function' },
  { label: 'CURRENT_TIMESTAMP', type: 'function' },
  { label: 'EXTRACT', type: 'function' },
  { label: 'DATE_TRUNC', type: 'function' },
  { label: 'DATE_FORMAT', type: 'function' },
  { label: 'DATEDIFF', type: 'function' },
  { label: 'DATEADD', type: 'function' },
  
  // 类型转换函数
  { label: 'CAST', type: 'function' },
  { label: 'CONVERT', type: 'function' },
  
  // 窗口函数
  { label: 'OVER', type: 'function' },
  { label: 'PARTITION BY', type: 'function' },
  { label: 'ROW_NUMBER', type: 'function' },
  { label: 'RANK', type: 'function' },
  { label: 'DENSE_RANK', type: 'function' },
  { label: 'NTILE', type: 'function' },
  { label: 'LEAD', type: 'function' },
  { label: 'LAG', type: 'function' },
  { label: 'FIRST_VALUE', type: 'function' },
  { label: 'LAST_VALUE', type: 'function' },
  
  // 条件聚合函数
  { label: 'GROUP_CONCAT', type: 'function' },
  { label: 'STRING_AGG', type: 'function' },
  { label: 'ARRAY_AGG', type: 'function' },
  { label: 'JSON_AGG', type: 'function' },
  
  // 常用数据类型
  { label: 'INT', type: 'type' },
  { label: 'INTEGER', type: 'type' },
  { label: 'BIGINT', type: 'type' },
  { label: 'SMALLINT', type: 'type' },
  { label: 'DECIMAL', type: 'type' },
  { label: 'NUMERIC', type: 'type' },
  { label: 'FLOAT', type: 'type' },
  { label: 'DOUBLE', type: 'type' },
  { label: 'REAL', type: 'type' },
  { label: 'VARCHAR', type: 'type' },
  { label: 'CHAR', type: 'type' },
  { label: 'TEXT', type: 'type' },
  { label: 'DATE', type: 'type' },
  { label: 'TIME', type: 'type' },
  { label: 'TIMESTAMP', type: 'type' },
  { label: 'BOOLEAN', type: 'type' },
  { label: 'BLOB', type: 'type' },
  { label: 'JSON', type: 'type' }
]

// 本地组件状态，用于存储从缓存中解析出的数据
const localAutocompleteData = ref({
  schemas: [],
  tables: [],
  columns: []
})

// 加载并解析缓存数据
const loadAndParseCache = () => {
  const dsId = selectedDatasourceId.value;
  if (!dsId) {
    console.warn('[loadAndParseCache] 没有选择数据源，跳过缓存加载');
    return;
  }

  const cacheKey = `autocomplete_cache_${dsId}`;
  console.log('[loadAndParseCache] 尝试加载缓存, key:', cacheKey);
  
  const cachedStr = localStorage.getItem(cacheKey);
  if (!cachedStr) {
    console.log('[loadAndParseCache] 未找到缓存数据');
    localAutocompleteData.value = { schemas: [], tables: [], columns: [] };
    return;
  }

  console.log('[loadAndParseCache] 原始缓存数据长度:', cachedStr.length);
  
  try {
    // 增加对 "undefined" 字符串的健壮性处理
    if (cachedStr === 'undefined' || cachedStr === 'null') {
      console.warn(`[loadAndParseCache] 发现无效缓存值 '${cachedStr}', 正在清除缓存`);
      localStorage.removeItem(cacheKey);
      localAutocompleteData.value = { schemas: [], tables: [], columns: [] };
      return;
    }

    // 尝试解析缓存数据
    let parsedData;
    try {
      parsedData = JSON.parse(cachedStr);
      console.log('[loadAndParseCache] 缓存解析成功, 数据类型:', typeof parsedData);
    } catch (parseError) {
      console.error('[loadAndParseCache] 缓存JSON解析失败:', parseError);
      localStorage.removeItem(cacheKey);
      localAutocompleteData.value = { schemas: [], tables: [], columns: [] };
      return;
    }
    
    // 安全地提取缓存数据，兼容多种可能的格式
    let cache = parsedData;
    
    // 如果缓存数据被包裹在data属性中或包含timestamp
    if (parsedData && typeof parsedData === 'object') {
      if (parsedData.data) {
        console.log('[loadAndParseCache] 检测到data包装的缓存格式，提取内部数据');
        cache = parsedData.data;
      } else if (parsedData.timestamp && parsedData.data) {
        console.log('[loadAndParseCache] 检测到带时间戳的缓存格式，提取内部数据');
        cache = parsedData.data;
      }
    }
    
    // 确保cache是对象类型
    if (!cache || typeof cache !== 'object') {
      console.warn('[loadAndParseCache] 缓存数据不是对象类型，无法处理:', cache);
      localStorage.removeItem(cacheKey);
      localAutocompleteData.value = { schemas: [], tables: [], columns: [] };
      return;
    }

    console.log('[loadAndParseCache] 开始处理缓存数据，包含schema数量:', Object.keys(cache).length);
    
    // 调用处理缓存数据的函数
    processAutocompleteCache(cache);
    
  } catch (e) {
    console.error('[loadAndParseCache] 处理缓存数据时发生错误:', e);
    localStorage.removeItem(cacheKey);
    localAutocompleteData.value = { schemas: [], tables: [], columns: [] };
  }
}

// 手动触发自动补全
const triggerAutocompletion = () => {
  // 检查组件是否已销毁
  if (isComponentDestroyed) {
    console.warn('[triggerAutocompletion] 组件已销毁，忽略自动补全触发');
    return;
  }
  
  if (!editorView || !editorView.contentDOM) {
    console.warn('[triggerAutocompletion] 编辑器实例或DOM不存在，无法触发自动补全');
    return;
  }
  
  try {
    console.log('[triggerAutocompletion] 手动触发自动补全');
    const autocompleteEvent = new KeyboardEvent('keydown', {
      key: ' ',
      ctrlKey: true,
      bubbles: true
    });
    editorView.contentDOM.dispatchEvent(autocompleteEvent);
  } catch (error) {
    console.error('[triggerAutocompletion] 触发自动补全时出错:', error);
  }
}

// 辅助函数：检查字符是否为英文点号(.)而非中文句号(。)
const isEnglishDot = (char) => {
  return char === '.';
}

// 辅助函数：检查文本是否以英文点号结尾
const endsWithEnglishDot = (text) => {
  return text.length > 0 && text[text.length - 1] === '.';
}

// 辅助函数：检查字符串是否包含英文点号，且前面是合法的标识符
const hasValidDotPattern = (text) => {
  // 匹配字母、数字、下划线后面跟着英文点号的模式
  return /\w+\./.test(text);
}

  // 辅助函数：格式化SQL语句用于显示在标签页上
const formatSqlForTabLabel = (sql) => {
  if (!sql) return '';
  
  // 删除多余空格并限制长度
  const cleanSql = sql.replace(/\s+/g, ' ').trim();
  const maxLength = 40; // 标签页上显示的最大长度
  
  if (cleanSql.length <= maxLength) {
    return cleanSql;
  }
  
  // 截断过长的SQL
  return cleanSql.substring(0, maxLength) + '...';
}

// 辅助函数：获取真实的SQL语句（只获取对应的那条SQL，避免叠加显示）
const getRealSqlForLabel = (result, index, defaultText) => {
  // 1. 首先尝试从result.sqls获取单条SQL（最可靠的方式）
  if (result.sqls && Array.isArray(result.sqls) && index < result.sqls.length && typeof result.sqls[index] === 'string') {
    return formatSqlForTabLabel(result.sqls[index]);
  }
  
  // 2. 对于单结果集执行，可以使用result.sql
  if ((!result.all_results || !Array.isArray(result.all_results) || result.all_results.length <= 1) && 
      typeof result.sql === 'string' && result.sql.trim()) {
    return formatSqlForTabLabel(result.sql);
  }
  
  // 3. 对于多SQL执行，尝试从选中的SQL或props.tab.sql中分割出单条SQL
  try {
    // 首先尝试从selectedSql分割
    if (typeof result.selectedSql === 'string' && result.selectedSql.trim()) {
      const statements = result.selectedSql.split(';').filter(stmt => stmt.trim().length > 0);
      if (index < statements.length) {
        return formatSqlForTabLabel(statements[index]);
      }
    }
    
    // 然后尝试从props.tab.sql分割
    if (typeof props.tab.sql === 'string' && props.tab.sql.trim()) {
      const statements = props.tab.sql.split(';').filter(stmt => stmt.trim().length > 0);
      if (index < statements.length) {
        return formatSqlForTabLabel(statements[index]);
      }
    }
  } catch (error) {
    console.error('分割SQL失败:', error);
  }
  
  // 4. 最后，使用默认文本
  return defaultText;
}

// 处理点号输入，提供数据库表名补全
const handleDotInput = () => {
  // 首先检查组件是否已销毁
  if (isComponentDestroyed) {
    console.warn('[handleDotInput] 组件已销毁，忽略点号处理');
    return;
  }
  
  if (!editorView) {
    console.warn('[handleDotInput] 编辑器实例不存在，无法处理点号');
    return;
  }
  
  try {
    // 获取当前编辑器内容和光标位置
    const state = editorView.state;
    const doc = state.doc;
    const selection = state.selection.main;
    const pos = selection.head;
    
    // 获取光标前的文本
    const textBefore = doc.sliceString(0, pos);
    
    // 检查最后一个字符是否为英文点号(.)，而不是中文句号(。)
    if (!endsWithEnglishDot(textBefore)) {
      console.log('[handleDotInput] 不是英文点号结尾，跳过特殊处理');
      return;
    }
    
    // 确认是有效的库名模式，如 "word."
    if (!hasValidDotPattern(textBefore)) {
      console.log('[handleDotInput] 不是有效的库名.模式，跳过特殊处理');
      return;
    }
    
    // 分析点号前的内容，找出可能的库名
    const words = textBefore.trim().split(/\s+/);
    const lastWord = words[words.length - 1];
    const possibleSchema = lastWord.slice(0, -1); // 去掉最后的点号
    
    // 确保库名是有效的标识符
    if (!/^\w+$/.test(possibleSchema)) {
      console.log(`[handleDotInput] "${possibleSchema}"不是有效的库名，跳过特殊处理`);
      return;
    }
    
    console.log(`[handleDotInput] 点号特殊处理：检测到可能的库名 "${possibleSchema}"`);
    
    // 确保缓存数据已加载
    if (localAutocompleteData.value.schemas.length === 0) {
      console.log('[handleDotInput] 点号处理时发现缓存为空，尝试加载');
      loadAndParseCache();
      
      // 如果仍然为空，就无法提供补全
      if (localAutocompleteData.value.schemas.length === 0) {
        console.warn('[handleDotInput] 缓存仍为空，无法提供表名补全');
        return;
      }
    }
    
    // 查找匹配的库
    const matchingSchemas = localAutocompleteData.value.schemas.filter(s => 
      s.label.toLowerCase() === possibleSchema.toLowerCase()
    );
    
    if (matchingSchemas.length === 0) {
      console.log(`[handleDotInput] 未找到匹配的库 "${possibleSchema}"`);
      
      // 仍然触发自动补全，但会显示库不存在的提示
      setTimeout(() => {
        // 再次检查组件是否已销毁
        if (isComponentDestroyed || !editorView) {
          console.warn('[handleDotInput] 延迟执行时组件已销毁，取消操作');
          return;
        }
        console.log('[handleDotInput] 点号特殊处理：显示库不存在提示');
        triggerAutocompletion();
      }, 50);
      
      return;
    }
    
    // 找到匹配的库，准备显示其下的表
    const schemaToMatch = matchingSchemas[0].label;
    console.log(`[handleDotInput] 找到匹配的库 "${schemaToMatch}"`);
    
    // 过滤出该库下的表
    const tablesInSchema = localAutocompleteData.value.tables.filter(t => 
      t.schema && t.schema.toLowerCase() === schemaToMatch.toLowerCase()
    );
    
    if (tablesInSchema.length === 0) {
      console.log(`[handleDotInput] 未在库 "${schemaToMatch}" 下找到任何表`);
      
      // 仍然触发自动补全，但会显示库下没有表的提示
      setTimeout(() => {
        // 再次检查组件是否已销毁
        if (isComponentDestroyed || !editorView) {
          console.warn('[handleDotInput] 延迟执行时组件已销毁，取消操作');
          return;
        }
        console.log('[handleDotInput] 点号特殊处理：显示库下没有表的提示');
        triggerAutocompletion();
      }, 50);
      
      return;
    }
    
    console.log(`[handleDotInput] 在库 "${schemaToMatch}" 下找到 ${tablesInSchema.length} 个表`);
    
    // 强制触发自动补全
    setTimeout(() => {
      // 再次检查组件是否已销毁
      if (isComponentDestroyed || !editorView) {
        console.warn('[handleDotInput] 延迟执行时组件已销毁，取消操作');
        return;
      }
      console.log('[handleDotInput] 点号特殊处理：强制触发自动补全');
      triggerAutocompletion();
    }, 50);
  } catch (error) {
    console.error('[handleDotInput] 处理点号输入时出错:', error);
  }
}

// 处理自动补全缓存数据
function processAutocompleteCache(cacheData) {
  console.log('处理自动补全缓存数据');
  
  if (!cacheData || typeof cacheData !== 'object') {
    console.warn('缓存数据无效，无法处理');
    return;
  }
  
  // 清空现有数据
  localAutocompleteData.value = {
    schemas: [],
    tables: [],
    columns: []
  };
  
  // 处理schemas
  const schemas = [];
  const tables = [];
  const columns = [];
  
  // 遍历每个schema
  Object.keys(cacheData).forEach(schemaName => {
    // 添加schema
    schemas.push({
      label: schemaName,
      type: 'schema'
    });
    
    // 如果schema包含表信息
    if (cacheData[schemaName] && typeof cacheData[schemaName] === 'object') {
      // 遍历该schema下的所有表
      Object.keys(cacheData[schemaName]).forEach(tableName => {
        // 添加表
        tables.push({
          label: tableName,
          schema: schemaName,
          type: 'table'
        });
        
        // 如果表包含列信息
        const tableInfo = cacheData[schemaName][tableName];
        if (tableInfo && tableInfo.columns && Array.isArray(tableInfo.columns)) {
          // 遍历表的所有列
          tableInfo.columns.forEach(column => {
            // 标准化列信息
            const columnName = typeof column === 'string' ? column : 
                              (column.name || column.column_name || column.Field);
            
            if (columnName) {
              columns.push({
                label: columnName,
                table: tableName,
                schema: schemaName,
                type: 'column'
              });
            }
          });
        }
      });
    }
  });
  
  // 更新本地数据
  localAutocompleteData.value.schemas = schemas;
  localAutocompleteData.value.tables = tables;
  localAutocompleteData.value.columns = columns;
  
  console.log(`缓存处理完成: ${schemas.length}个schemas, ${tables.length}个tables, ${columns.length}个columns`);
}

// 监听数据源变化，重新加载缓存
watch(selectedDatasourceId, (newVal) => {
  console.log(`数据源变更为: ${newVal}, 重新加载缓存`);
  loadAndParseCache();
}, { immediate: true });

// SQL自动补全函数
function sqlCompletions(context) {
  // 获取当前输入的文本
  const beforeCursor = context.state.doc.sliceString(0, context.pos);
  
  // 检查是否需要刷新缓存
  if (localAutocompleteData.value.schemas.length === 0) {
    console.log('自动补全触发时发现缓存为空，尝试加载');
    loadAndParseCache();
  }
  
  // 调试输出当前输入
  console.log('当前输入文本:', beforeCursor);

  // 构建基础补全选项
  let options = [];
  
  // 获取当前上下文中提到的所有表名
  const tableMatches = beforeCursor.match(/from\s+(\w+(?:\.\w+)?)|join\s+(\w+(?:\.\w+)?)|update\s+(\w+(?:\.\w+)?)|into\s+(\w+(?:\.\w+)?)/gi) || [];
  const mentionedTables = tableMatches
    .map(match => {
      const parts = match.split(/[\s,()=<>!]+/).pop().split('.');
      return parts.length > 1 ? parts[1] : parts[0];
    })
    .filter(Boolean)
    .map(table => table.toLowerCase());

  console.log('当前SQL中提到的表:', mentionedTables);

  // 获取最后一个单词，用于匹配
  const lastWord = beforeCursor.split(/[\s,()=<>!]+/).pop() || '';
  console.log('当前输入的最后一个单词:', lastWord);

  // 判断是否在 FROM/JOIN 等子句中
  const isInTableContext = /\b(from|join|update|into)\s+[^;]*$/i.test(beforeCursor);
  console.log('是否在表上下文中:', isInTableContext);

  // 判断是否在 WHERE, SELECT 等子句中
  const isInConditionClause = /\b(where|select|group\s+by|order\s+by|having)\s+[^;]*$/i.test(beforeCursor);
  console.log('是否在条件子句中:', isInConditionClause);

  // 1. 处理库名.表名.字段名的模式 (schema.table.field)
  const fullMatch = beforeCursor.match(/(\w+)\.(\w+)\.(\w*)$/);
  if (fullMatch) {
    const [_, schemaName, tableName, columnPart] = fullMatch;
    console.log(`检测到库名.表名.字段名模式: schema=${schemaName}, table=${tableName}, column=${columnPart}`);
    
    // 查找匹配的表的字段
    const matchingColumns = localAutocompleteData.value.columns.filter(
      c => c.schema.toLowerCase() === schemaName.toLowerCase() && 
           c.table.toLowerCase() === tableName.toLowerCase()
    );
    
    if (matchingColumns.length > 0) {
      options = matchingColumns.map(column => ({
        label: `【字段】${column.label}`,
        type: 'column',
        boost: 100,
        apply: column.label
      }));
      
      return {
        from: context.pos - (columnPart?.length || 0),
        options,
        validFor: /^\w*$/
      };
    }
  }
  
  // 2. 处理表名.字段名模式 (table.field)
  const tableColumnMatch = beforeCursor.match(/(\w+)\.(\w*)$/);
  if (tableColumnMatch) {
    const [_, tableName, columnPart] = tableColumnMatch;
    console.log(`检测到表名.字段名模式: table=${tableName}, columnPart=${columnPart}`);
    
    // 首先尝试匹配为"库名.表名"模式
    const matchingTables = localAutocompleteData.value.tables.filter(
      t => t.schema.toLowerCase() === tableName.toLowerCase()
    );
    
    // 如果匹配到库名，则展示表名
    if (matchingTables.length > 0) {
      options = matchingTables.map(table => ({
        label: `【表】${table.label}`,
        type: 'table',
        boost: 90,
        apply: table.label
      }));
      
      return {
        from: context.pos - (columnPart?.length || 0),
        options,
        validFor: /^\w*$/
      };
    }
    
    // 如果不是库名，则按照"表名.字段名"模式处理
    // 查找匹配的表的字段
    const matchingColumns = localAutocompleteData.value.columns.filter(
      c => c.table.toLowerCase() === tableName.toLowerCase() &&
           (c.schema === selectedSchema.value || c.schema.toLowerCase() === tableName.toLowerCase())
    );
    
    if (matchingColumns.length > 0) {
      options = matchingColumns.map(column => ({
        label: `【字段】${column.label}`,
        type: 'column',
        boost: 100,
        apply: column.label
      }));
      
      return {
        from: context.pos - (columnPart?.length || 0),
        options,
        validFor: /^\w*$/
      };
    }
  }

  // 3. 处理普通输入
  // 在表上下文中（FROM/JOIN等子句后），优先显示库名和表名
  if (isInTableContext) {
    options = [
      // 添加所有库名
      ...localAutocompleteData.value.schemas.map(schema => ({
        label: `【库】${schema.label}`,
        type: 'schema',
        boost: 100,
        apply: schema.label
      })),
      
      // 添加当前 schema 下的表名
      ...localAutocompleteData.value.tables
        .filter(table => table.schema === selectedSchema.value)
        .map(table => ({
          label: `【表】${table.label}`,
          type: 'table',
          boost: 90,
          apply: table.label
        }))
    ];
  } 
  // 在条件子句中，优先显示字段名
  else if (isInConditionClause || mentionedTables.length > 0) {
    // 添加相关表的字段
    const relevantColumns = localAutocompleteData.value.columns.filter(column => {
      // 如果SQL中提到了这个表，则包含其字段
      if (mentionedTables.includes(column.table.toLowerCase())) {
        return true;
      }
      
      // 或者是当前选择的schema下的字段
      if (column.schema === selectedSchema.value) {
        return true;
      }
      
      return false;
    });
    
    options = relevantColumns.map(column => ({
      label: `【字段】${column.label}`,
      type: 'column',
      boost: 100,
      apply: column.label
    }));
  } else {
    // 默认情况下也包含字段，确保在任何位置都能找到字段
    options = [
      // 添加当前 schema 下所有表的字段
      ...localAutocompleteData.value.columns
        .filter(column => column.schema === selectedSchema.value)
        .map(column => ({
          label: `【字段】${column.label} (${column.table})`,
          type: 'column',
          boost: 90,
          apply: column.label
        })),
      
      // 添加所有库名
      ...localAutocompleteData.value.schemas.map(schema => ({
        label: `【库】${schema.label}`,
        type: 'schema',
        boost: 100,
        apply: schema.label
      })),
      
      // 添加当前 schema 下的表名
      ...localAutocompleteData.value.tables
        .filter(table => table.schema === selectedSchema.value)
        .map(table => ({
          label: `【表】${table.label}`,
          type: 'table',
          boost: 95,
          apply: table.label
        }))
    ];
  }

  // 始终添加 SQL 关键字
  options = [
    ...options,
    // 添加 SQL 关键字
    ...sqlKeywords.map(k => ({
      label: `【${k.type === 'keyword' ? '关键字' : 
              k.type === 'function' ? '函数' : 
              k.type === 'operator' ? '运算符' : 
              k.type === 'type' ? '类型' : k.type}】${k.label}`,
      type: k.type,
      boost: k.type === 'keyword' ? 95 : 70,
      apply: k.label
    }))
  ];

  // 过滤匹配当前输入的选项
  if (lastWord) {
    const searchText = lastWord.toLowerCase();
    options = options
      .filter(opt => {
        // 移除标签前缀后再进行匹配
        const plainLabel = opt.label.replace(/【.*?】/, '').toLowerCase();
        const plainApply = opt.apply.toLowerCase();
        
        return plainLabel.includes(searchText) || plainApply.includes(searchText);
      })
      .sort((a, b) => {
        // 1. 首先按是否以搜索文本开头排序
        const aStartsWith = a.apply.toLowerCase().startsWith(searchText);
        const bStartsWith = b.apply.toLowerCase().startsWith(searchText);
        if (aStartsWith !== bStartsWith) {
          return bStartsWith ? 1 : -1;
        }

        // 2. 然后按类型排序
        if (a.type !== b.type) {
          if (isInTableContext) {
            // 在表上下文中，库名优先
            if (a.type === 'schema') return -1;
            if (b.type === 'schema') return 1;
          } else if (isInConditionClause) {
            // 在条件子句中，字段名优先
            if (a.type === 'column') return -1;
            if (b.type === 'column') return 1;
          }
        }

        // 3. 最后按boost值排序
        return (b.boost || 0) - (a.boost || 0);
      });

    return {
      from: context.pos - lastWord.length,
      options,
      validFor: /^\w*$/
    };
  }

  return {
    from: context.pos,
    options,
    validFor: /^\w*$/
  };
}

// 带有语法高亮的SQL主题
const sqlHighlightStyle = HighlightStyle.define([
  { tag: tags.keyword, color: '#d73a49' },
  { tag: tags.operator, color: '#d73a49' },
  { tag: tags.string, color: '#032f62' },
  { tag: tags.number, color: '#005cc5' },
  { tag: tags.comment, color: '#6a737d', fontStyle: 'italic' },
  { tag: tags.function(tags.variableName), color: '#6f42c1' },
  { tag: tags.typeName, color: '#22863a' },
  { tag: tags.propertyName, color: '#e36209' },
  { tag: tags.variableName, color: '#e36209' },
])

// 创建基本主题
const basicTheme = EditorView.theme({
  '&': {
    height: '100%',
    fontSize: '14px',
    fontFamily: 'Consolas, "Courier New", monospace',
    backgroundColor: '#ffffff',
    color: '#212529'
  },
  '.cm-scroller': {
    overflow: 'auto',
    fontFamily: 'inherit'
  },
  '.cm-content': {
    caretColor: '#000000'
  },
  '.cm-cursor': {
    borderLeftColor: '#000000'
  },
  '.cm-activeLine': {
    backgroundColor: '#f1f1f1'
  },
  '&.cm-focused .cm-activeLine': { 
    backgroundColor: '#e8f2ff'
  },
  '.cm-activeLineGutter': {
    backgroundColor: '#f1f1f1'
  },
  '.cm-gutters': {
    backgroundColor: '#f7f7f7',
    color: '#6c757d',
    borderRight: '1px solid #dee2e6'
  },
  '.cm-lineNumbers': {
    minWidth: '3em',
    color: '#6c757d'
  },
  '.cm-selectionMatch': { 
    backgroundColor: '#a2c9ff'
  },
  '.cm-tooltip': {
    border: '1px solid #dee2e6',
    backgroundColor: '#ffffff'
  },
  '.cm-tooltip-autocomplete': {
    '& > ul': {
      backgroundColor: '#ffffff',
      border: '1px solid #dee2e6',
      fontSize: '14px',
      fontFamily: 'inherit',
      maxHeight: '300px'
    },
    '& > ul > li': {
      padding: '4px 8px',
      color: '#212529'
    },
    '& > ul > li[aria-selected]': {
      backgroundColor: '#0d6efd',
      color: '#ffffff'
    }
  }
})

// 一个标志位，用于标记组件是否已经被销毁
let isComponentDestroyed = false;

// 初始化CodeMirror编辑器
const initCodeMirror = () => {
  if (!cmEditor.value) {
    console.warn('[initCodeMirror] cmEditor ref 不存在，无法初始化编辑器');
    return;
  }
  
  console.log('[initCodeMirror] 开始初始化编辑器，添加调试事件监听器');
  // 添加调试键盘快捷键 (Ctrl+Alt+D)，用于测试选择功能
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.altKey && e.key === 'd') {
      console.log('[DEBUG] 触发调试选择');
      if (editorView) {
        try {
          // 创建一个选择，选择第一条SQL语句
          const text = editorView.state.doc.toString();
          const firstSemicolon = text.indexOf(';');
          const end = firstSemicolon > 0 ? firstSemicolon : (text.length > 30 ? 30 : text.length);
          
          // 获取前30个字符或第一个分号前的内容
          const selectedText = text.substring(0, end);
          console.log('[DEBUG] 模拟选择文本:', selectedText);
          
          // 创建一个新的编辑器状态，带有选择
          const newState = EditorState.create({
            doc: text,
            selection: {anchor: 0, head: end}
          });
          
          // 派发状态变更
          editorView.dispatch({selection: {anchor: 0, head: end}});
          console.log('[DEBUG] 已创建选择，范围: 0-', end);
          
          // 测试选择是否成功
          setTimeout(() => {
            const selection = editorView.getSelection();
            console.log('[DEBUG] 测试选择结果:', selection);
          }, 100);
        } catch (error) {
          console.error('[DEBUG] 创建测试选择时出错:', error);
        }
      }
    }
  });

  // 确保只初始化一次
  if (editorView) {
    console.log('[initCodeMirror] 编辑器已经存在，跳过初始化');
    return;
  }

  try {
    console.log('[initCodeMirror] 开始初始化编辑器');
    
    // 包装 requestMeasure 方法，增加安全检查
    const safeRequestMeasure = (original) => {
      return function(...args) {
        if (isComponentDestroyed) {
          console.warn('[safeRequestMeasure] 组件已销毁，阻止测量请求');
          return null;
        }
        try {
          return original.apply(this, args);
        } catch (e) {
          console.error('[safeRequestMeasure] 测量请求出错:', e);
          return null;
        }
      };
    };

    // 创建自定义视图更新监听器，添加错误处理
    const safeUpdateListener = EditorView.updateListener.of(update => {
      try {
        if (isComponentDestroyed) {
          console.warn('[safeUpdateListener] 组件已销毁，忽略更新');
          return;
        }
        
        if (update.docChanged) {
          const newContent = update.state.doc.toString();
          if (sqlContent.value !== newContent) {
            sqlContent.value = newContent;
            handleSqlChange(newContent);
          }
        }
      } catch (e) {
        console.error('[safeUpdateListener] 处理编辑器更新时出错:', e);
      }
    });

    const state = EditorState.create({
      doc: sqlContent.value || '',
      extensions: [
        EditorView.lineWrapping,
        history(),
        EditorState.allowMultipleSelections.of(true),
        keymap.of([
          ...defaultKeymap,
          ...historyKeymap,
          ...completionKeymap,
          indentWithTab
        ]),
        placeholder('请输入 SQL 查询语句'),
        basicTheme,
        syntaxHighlighting(sqlHighlightStyle),
        
        // 禁用默认的SQL自动补全
        sql({
          upperCaseKeywords: true,
          autocomplete: []
        }),

        // 使用完全自定义的补全逻辑
        autocompletion({
          activateOnTyping: true,
          override: [sqlCompletions],
          // 关闭自定义渲染，完全依赖前面修改后的标签显示
          defaultKeymap: true,
          icons: true,
          // 自动触发设置
          maxRenderedOptions: 300,
          activateOnSpecialChar: true, // 特殊字符时激活
          updateSyncTime: 50 // 更新速度更快
        }),
        
        // 使用我们的安全更新监听器替代原始的监听器
        safeUpdateListener
      ]
    });

    // 创建EditorView
    try {
      editorView = new EditorView({
        state,
        parent: cmEditor.value
      });
      
      // 替换 requestMeasure 方法为安全版本
      const originalRequestMeasure = editorView.requestMeasure;
      editorView.requestMeasure = safeRequestMeasure(originalRequestMeasure);
      
      // 添加getSelection方法，用于获取当前选中的文本
      editorView.getSelection = function() {
        try {
          if (!this.state || !this.state.selection) {
            return '';
          }
          const selection = this.state.selection.main;
          if (selection.empty) return '';
          return this.state.doc.sliceString(selection.from, selection.to);
        } catch (e) {
          console.error('获取选中文本失败:', e);
          return '';
        }
      };
      
      console.log('[initCodeMirror] 编辑器创建成功，并添加了getSelection方法');
    } catch (viewError) {
      console.error('[initCodeMirror] 创建编辑器视图失败:', viewError);
      return;
    }
    
    // 添加点号键监听器 - 增加组件销毁检查
    const dotKeyHandler = (event) => {
      if (isComponentDestroyed) {
        console.warn('[dotKeyHandler] 组件已销毁，忽略点号键事件');
        return;
      }
      
      // 只处理英文点号，不处理中文句号
      if (event.key === '.' && event.code === 'Period') {
        console.log('检测到英文点号键被按下，准备触发自动补全');
        // 确保缓存已加载
        forceRefreshAutocompleteCache().then(success => {
          if (isComponentDestroyed) return; // 再次检查组件是否已销毁
          
          if (success) {
            // 延迟一小段时间等待点号实际输入到编辑器
            setTimeout(() => {
              if (isComponentDestroyed) return; // 延迟执行前再次检查
              
              console.log('点号延迟触发：强制启动自动补全');
              handleDotInput(); // 使用专门的点号处理函数
            }, 100);
          } else {
            console.warn('缓存刷新失败，补全可能无法正常工作');
          }
        });
      }
    };
    
    // 添加点号输入后的焦点监听器 - 增加组件销毁检查
    const inputHandler = (event) => {
      if (isComponentDestroyed) {
        console.warn('[inputHandler] 组件已销毁，忽略输入事件');
        return;
      }
      
      const value = event.target.value || (editorView && editorView.state ? editorView.state.doc.toString() : '');
      // 获取最后输入的字符
      const lastChar = value.slice(-1);
      
      // 只处理英文点号(.)，不处理中文句号(。)
      if (isEnglishDot(lastChar)) {
        console.log('检测到输入事件中的英文点号字符，准备触发自动补全');
        setTimeout(() => {
          if (isComponentDestroyed) return; // 延迟执行前检查组件是否已销毁
          
          console.log('输入事件延迟触发：处理点号');
          handleDotInput(); // 使用专门的点号处理函数
        }, 50);
      }
    };
    
    // 使用 try-catch 添加事件监听器
    try {
      editorView.dom.addEventListener('keydown', dotKeyHandler);
      editorView.dom.addEventListener('input', inputHandler);
      
      // 保存事件处理函数引用，以便在销毁时移除
      editorView._dotKeyHandler = dotKeyHandler;
      editorView._inputHandler = inputHandler;
      
      console.log('[initCodeMirror] 已添加编辑器事件监听器');
    } catch (eventError) {
      console.error('[initCodeMirror] 添加事件监听器失败:', eventError);
    }
    
    // 确保缓存已加载
    console.log(`编辑器初始化完成，检查缓存状态: schemas=${localAutocompleteData.value.schemas.length}, tables=${localAutocompleteData.value.tables.length}`);
    if (localAutocompleteData.value.schemas.length === 0) {
      console.log('编辑器初始化时发现缓存为空，尝试重新加载');
      loadAndParseCache();
    }
    
    console.log('[initCodeMirror] 编辑器初始化完成');
  } catch (error) {
    console.error('[initCodeMirror] 初始化编辑器时出错:', error);
    // 清理可能部分创建的编辑器
    if (editorView) {
      try {
        editorView.destroy();
      } catch (e) {}
      editorView = null;
    }
  }
};

// 监听sqlContent变化，更新编辑器内容
watch(() => props.tab.sql, (newSql) => {
  if (editorView && newSql !== editorView.state.doc.toString()) {
    editorView.dispatch({
      changes: {
        from: 0,
        to: editorView.state.doc.length,
        insert: newSql || ''
      }
    })
  }
})

const hasContent = computed(() => {
  return sqlContent.value?.trim().length > 0
})

// 计算分页组件的总行数
const totalRows = computed(() => {
  // 如果有明确的总行数，使用它
  if (props.tab.rowCount !== undefined && props.tab.rowCount !== null) {
    return props.tab.rowCount;
  }
  
  // 使用实际结果数量，无论是否有LIMIT
  return props.tab.results?.length || 0;
})

// 添加客户端分页计算属性，根据当前页和页大小过滤数据
const paginatedResults = computed(() => {
  console.log('[paginatedResults] 计算分页数据:', {
    resultsLength: props.tab.results?.length,
    currentPage: currentPage.value,
    pageSize: pageSize.value,
    isArray: Array.isArray(props.tab.results)
  });
  
  if (!props.tab.results || !Array.isArray(props.tab.results)) {
    console.log('[paginatedResults] 无有效数据，返回空数组');
    return [];
  }
  
  const startIndex = (currentPage.value - 1) * pageSize.value;
  const endIndex = startIndex + pageSize.value;
  const sliced = props.tab.results.slice(startIndex, endIndex);
  
  console.log('[paginatedResults] 分页结果:', {
    startIndex,
    endIndex,
    slicedLength: sliced.length,
    firstRow: sliced[0]
  });
  
  return sliced;
})

const availableSchemas = ref([]) // 本地状态，不再是计算属性
const isSchemasLoading = ref(false) // 用于控制加载状态

// 新增：获取并设置Schemas
const fetchAndSetSchemas = async (dsId) => {
  if (!dsId) {
    availableSchemas.value = []
    selectedSchema.value = null
    return
  }
  isSchemasLoading.value = true
  try {
    const response = await getSchemaList(dsId)
    console.log('Schema API 响应:', response)
    
    let schemaNames = []
    
    // 处理各种可能的返回格式
    if (Array.isArray(response)) {
      // 如果直接返回数组，直接使用
      schemaNames = response
    } else if (response && typeof response === 'object') {
      // 如果返回对象
      if (response.schemas && Array.isArray(response.schemas)) {
        // 如果有 schemas 字段且是数组，使用它
        schemaNames = response.schemas
      } else if (response.data && Array.isArray(response.data)) {
        // 如果有 data 字段且是数组，使用它
        schemaNames = response.data
      } else if (response.data && response.data.schemas && Array.isArray(response.data.schemas)) {
        // 如果在 data.schemas 中是数组，使用它
        schemaNames = response.data.schemas
      } else {
        // 否则尝试使用对象的所有键，但排除特定的非 schema 键
        const keys = Object.keys(response)
        schemaNames = keys.filter(key => 
          !['success', 'code', 'message', 'msg', 'status', 'error'].includes(key.toLowerCase())
        )
      }
    }
    
    console.log('处理后的 schema 名称列表:', schemaNames)
    availableSchemas.value = schemaNames.map(name => ({ label: name, value: name }))
    
    // 如果有可用的 schema，总是选择一个（优先使用已选择的，或者第一个）
    if (availableSchemas.value.length > 0) {
      const currentSchemaIsValid = availableSchemas.value.some(s => s.value === selectedSchema.value)
      
      if (!currentSchemaIsValid || !selectedSchema.value) {
        // 如果当前没有选择有效的 schema，选择第一个
        selectedSchema.value = availableSchemas.value[0].value
        console.log(`自动选择第一个 schema: ${selectedSchema.value}`)
      } else {
        console.log(`保持当前选择的 schema: ${selectedSchema.value}`)
      }
      
      // 无论如何，都通知父组件当前的 schema 选择
      nextTick(() => {
        notifyParentOfSchemaChange()
        
        // 直接更新父组件中的 tab 对象
        if (props.tab) {
          emit('update:tab', {
            ...props.tab,
            datasourceId: dsId,
            schema: selectedSchema.value
          })
        }
      })
    } else {
      // 如果没有可用的 schema
      selectedSchema.value = null
      console.log('没有可用的 schema')
    }

  } catch (error) {
    console.error('获取 Schemas 失败:', error)
    ElMessage.error('获取 Schema 列表失败')
    availableSchemas.value = []
    selectedSchema.value = null
  } finally {
    isSchemasLoading.value = false
  }
}

// 创建一个单独的函数来通知父组件 schema 变更
const notifyParentOfSchemaChange = () => {
  if (!selectedSchema.value || !selectedDatasourceId.value) {
    console.log('无法通知父组件: schema 或 datasourceId 为空')
    return
  }
  
  console.log(`通知父组件刷新数据库对象: datasourceId=${selectedDatasourceId.value}, schema=${selectedSchema.value}`)
  emit('tab-database-change', {
    tabId: props.tab.id,
    datasourceId: selectedDatasourceId.value,
    schema: selectedSchema.value
  })
}

// 监听数据源变化
watch(() => props.tab.datasourceId, (newVal) => {
  selectedDatasourceId.value = newVal
  // 当数据源变化时，获取新的schema列表
  fetchAndSetSchemas(newVal)
}, { immediate: true })

// 监听 schema 变化
watch(() => props.tab.schema, (newVal) => {
  if (newVal !== selectedSchema.value) {
    selectedSchema.value = newVal
    
    // 如果 schema 是由外部设置的，主动通知父组件
    if (newVal) {
      nextTick(() => {
        notifyParentOfSchemaChange()
      })
    }
  }
})

// 处理 schema 变化
const handleSchemaChange = (value) => {
  console.log(`处理schema变更: ${selectedSchema.value} -> ${value}`);
  selectedSchema.value = value;
  
  // 发送更完整的变更事件，确保包含数据源ID和schema
  emit('tab-database-change', {
    tabId: props.tab.id,
    datasourceId: selectedDatasourceId.value,
    schema: value
  });
  
  // 同时通过update:tab直接更新父组件中的tab对象
  if (props.tab) {
    emit('update:tab', {
      ...props.tab,
      datasourceId: selectedDatasourceId.value,
      schema: value
    });
  }
  
  console.log(`已发送 schema 变更事件: tabId=${props.tab.id}, datasourceId=${selectedDatasourceId.value}, schema=${value}`);
}

// 监听 tab.sql 变化，更新本地 sqlContent
watch(() => props.tab.sql, (newVal) => {
  sqlContent.value = newVal || ''
})

// 定义一个函数来在必要时强制重置执行状态
const forceResetExecutingState = () => {
  if (isExecuting.value || (props.tab && props.tab.isExecuting)) {

    
    // 重置所有相关状态
    if (props.tab) {
      props.tab.isExecuting = false;
      emit('update:tab', {...props.tab});
    }
    
    // 发送状态变更事件
    emit('execution-state-change', false);
    
    // 确保界面反映状态变化
    nextTick(() => {
      if (props.tab && props.tab.isExecuting) {

        props.tab.isExecuting = false;
        emit('update:tab', {...props.tab});
      }
    });
  }
};

// 设置一个全局安全超时，确保查询不会无限执行
let safetyTimeoutId = null;

// 监听isExecuting值的变化
watch(() => isExecuting.value, (newVal) => {
  if (newVal) {
    // 当开始执行时，设置一个安全超时
    if (safetyTimeoutId) clearTimeout(safetyTimeoutId);
    
    safetyTimeoutId = setTimeout(() => {

      forceResetExecutingState();
    }, 30000); // 30秒超时
  } else {
    // 当结束执行时，清除安全超时
    if (safetyTimeoutId) {
      clearTimeout(safetyTimeoutId);
      safetyTimeoutId = null;
    }
  }
});

// 单独监听错误状态
watch(() => props.tab.error, (newError) => {
  if (isExecuting.value) {

    resetExecutingStateWithDelay();
  }
  
  // 只有在严重错误时才切换到输出标签页
  if (newError && newError.toString().toLowerCase().includes('fatal')) {

    nextTick(() => {
      activeResultTab.value = 'output';
    });
  }
  // 其他情况下，保持在结果标签页，即使结果为空
});

// 定义一个方法来重置执行状态，并确保至少显示一段时间
const resetExecutingStateWithDelay = () => {
  const elapsedTime = Date.now() - queryStartTime;
  const minDisplayTime = 300; // ms
  const remainingTime = Math.max(0, minDisplayTime - elapsedTime);

  setTimeout(() => {
    isExecuting.value = false;

  }, remainingTime);
};

// 重置执行状态的函数 - 更完善的重置逻辑
const resetExecutingState = () => {
  // 直接修改props中的执行状态
  if (props.tab) {
    props.tab.isExecuting = false;
    emit('update:tab', {...props.tab});
  }
  
  // 同时触发状态变更事件
  emit('execution-state-change', false);
  
  // 重置本地状态标志
  isMultiStatement.value = false;
  

};
// 直接执行查询的函数，返回Promise
const handleExecuteQueryDirectly = (queryParams, disableUIUpdate = false) => {
  console.log('====== handleExecuteQueryDirectly 开始 ======');
  console.log('disableUIUpdate 参数:', disableUIUpdate);
  console.log('queryParams:', queryParams);

  return new Promise((resolve, reject) => {
    const originalSql = sqlContent.value;
    let timeoutId;
    let startTime = Date.now(); // 记录查询开始时间

    // console.log('Execute Query Directly 参数:', JSON.stringify(queryParams));
    
    // 处理SQL语句
    if (queryParams.sql && typeof queryParams.sql === 'string') {
      // 检查是否是多SQL语句
      const isMultiStatement = queryParams.isMultiStatement || 
                              (queryParams.sql.includes(';') && 
                               queryParams.sql.split(';').filter(s => s.trim()).length > 1);
      
      if (isMultiStatement) {
        // 多SQL语句执行时，保留分号
        const trimmedSql = queryParams.sql.trim();
        
        // 分割SQL语句
        const sqlStatements = [];
        let currentStatement = '';
        let inSingleQuote = false;
        let inDoubleQuote = false;
        let inBacktick = false;
        let escaped = false;

        for (let i = 0; i < trimmedSql.length; i++) {
          const char = trimmedSql[i];
          if (escaped) {
            currentStatement += char;
            escaped = false;
            continue;
          }
          if (char === '\\') {
            currentStatement += char;
            escaped = true;
            continue;
          }
          if (char === "'" && !inDoubleQuote && !inBacktick) {
            currentStatement += char;
            inSingleQuote = !inSingleQuote;
            continue;
          }
          if (char === '"' && !inSingleQuote && !inBacktick) {
            currentStatement += char;
            inDoubleQuote = !inDoubleQuote;
            continue;
          }
          if (char === '`' && !inSingleQuote && !inDoubleQuote) {
            currentStatement += char;
            inBacktick = !inBacktick;
            continue;
          }
          if (char === ';' && !inSingleQuote && !inDoubleQuote && !inBacktick) {
            if (currentStatement.trim()) {
              sqlStatements.push(currentStatement.trim());
            }
            currentStatement = '';
            continue;
          }
          currentStatement += char;
        }
        if (currentStatement.trim()) {
          sqlStatements.push(currentStatement.trim());
        }
        
        // 过滤掉空语句
        const filteredStatements = sqlStatements.filter(sql => sql.trim().length > 0);
        
        console.log('handleExecuteQueryDirectly - 多SQL语句执行，保留原始SQL:', trimmedSql);
        console.log('handleExecuteQueryDirectly - 分割后的SQL语句:', filteredStatements);
        
        queryParams = {
          ...queryParams,
          sql: trimmedSql,
          isMultiStatement: true,
          splitSqlStatements: filteredStatements // 关键：传递分割后的SQL
        };
      } else {
        // 单SQL语句执行时，移除末尾分号
        const cleanSql = queryParams.sql.trim().endsWith(';') 
          ? queryParams.sql.trim().slice(0, -1) 
          : queryParams.sql.trim();
        
        queryParams = {
          ...queryParams,
          sql: cleanSql
        };
        console.log('handleExecuteQueryDirectly - 单SQL语句执行，清理后的SQL:', cleanSql);
      }
    }
    
    // 确保参数名称与后端API匹配
    const apiParams = {
      ...queryParams,
      sql_content: queryParams.sql, // 将sql改为sql_content
      datasource_id: queryParams.datasourceId, // 将datasourceId改为datasource_id
      is_multi_statement: true // 始终启用多SQL语句支持
    };
    
    // 删除不匹配后端API的参数
    delete apiParams.sql;
    delete apiParams.datasourceId;
    
    // 设置一个超时处理，防止永远等待
    timeoutId = setTimeout(() => {
      cleanup();
      reject(new Error('查询执行超时，请检查SQL语句或数据库连接'));
    }, 30000); // 30秒超时
    
    // 监听函数
    const resultListener = (event) => {
      if (event && event.type === 'query-result' && 
          event.detail && event.detail.tabId === props.tab.id) {
        
        const result = event.detail;
        cleanup();
        
        if (result.error) {
          reject(new Error(result.error));
        } else {
          // 只有在不禁用UI更新时才更新标签页
          if (!disableUIUpdate) {
            // 更新当前标签页的结果 - 即使没有结果也更新
            props.tab.results = result.results || [];
            props.tab.columns = result.columns || [];
            props.tab.rowCount = result.rowCount || (result.results ? result.results.length : 0);
            props.tab.messages = result.messages || '';

            // 检查SQL类型
            const sqlLower = queryParams.sql?.trim().toLowerCase() || '';
            const isDDL = sqlLower.startsWith('create ') ||
                         sqlLower.startsWith('alter ') ||
                         sqlLower.startsWith('drop ') ||
                         sqlLower.startsWith('truncate ') ||
                         sqlLower.startsWith('rename ');

            // 如果是DDL语句，确保有成功消息
            if (isDDL && (!result.results || result.results.length === 0)) {
              props.tab.messages = result.messages || '执行成功！';
              console.log('DDL语句执行成功，设置消息:', props.tab.messages);
            }

            // 确保如果有结果，自动切换到第一个结果标签页
            if (result.results && result.results.length > 0) {
              activeResultTab.value = 'result-tab-1';
            } else {
              activeResultTab.value = 'output';
            }

            // 更新标签页
            emit('update:tab', {...props.tab});
          }

          // 返回完整的结果给调用者
          resolve(result);
        }
      }
    };
    
    // 清理函数
    const cleanup = () => {
      clearTimeout(timeoutId);
      document.removeEventListener('query-result', resultListener);
    };
    
    // 添加结果监听器
    document.addEventListener('query-result', resultListener);
    
    // 发送查询请求
    console.log('直接执行查询, 参数:', apiParams);
    
    // 从API模块导入原始查询函数
    import('@/api/query').then(({ executeQuery }) => {
      console.log('已导入executeQuery API函数');
      executeQuery(apiParams).then(response => {
        console.log('API直接查询成功:', response);
        console.log('查询结果详情:', {
          hasResults: !!(response.results && response.results.length),
          resultCount: response.results ? response.results.length : 0,
          firstRow: response.results && response.results.length > 0 ? JSON.stringify(response.results[0]) : null,
          columns: response.columns ? response.columns.join(', ') : '无列信息'
        });
        
        // 确保response包含results属性，即使是空数组
        if (!response.results) {
          console.log('结果中没有results属性，添加空数组');
          response.results = [];
        }
        
                  // 处理API响应中的数据格式
          // console.log('API响应原始数据结构:', JSON.stringify(response));
          
          // 检查是否有多结果集数据
          if (response.all_results && Array.isArray(response.all_results) && response.all_results.length > 0) {
            console.log('检测到多结果集数据，共 ' + response.all_results.length + ' 个结果集');
            
            // 保留原始的多结果集数据，供后续处理
            response.is_multi_statement = true;
            
            // 使用第一个结果集作为主要结果
            const firstResult = response.all_results[0];
            if (firstResult) {
              response.results = firstResult.rows || [];
              response.columns = firstResult.columns || [];
              console.log(`设置第一个结果集作为主结果，共 ${response.results.length} 行`);
            }
          }
          
          // 如果结果是空数组或不存在，检查是否有原始数据在其他属性中
          if ((!response.results || response.results.length === 0) && response.data) {
            console.log('results为空或不存在，检测到data属性，尝试使用data数据');
            
            // 检查data是否有多结果集
            if (response.data.all_results && Array.isArray(response.data.all_results) && response.data.all_results.length > 0) {
              console.log('data中检测到多结果集数据，共 ' + response.data.all_results.length + ' 个结果集');
              
              // 保留原始的多结果集数据
              response.all_results = response.data.all_results;
              response.is_multi_statement = true;
              
              // 使用第一个结果集作为主要结果
              const firstResult = response.data.all_results[0];
              if (firstResult) {
                response.results = firstResult.rows || [];
                response.columns = firstResult.columns || [];
                console.log(`设置data中第一个结果集作为主结果，共 ${response.results.length} 行`);
              }
            }
            // 处理常规数据结构
            else if (Array.isArray(response.data)) {
              // 如果data本身是数组，直接使用
              response.results = response.data;
              console.log('data是数组，直接使用');
            } else if (typeof response.data === 'object') {
              // 检查data对象的不同可能属性
              if (response.data.rows) {
                // 如果有rows属性，使用它
                response.results = response.data.rows;
                console.log('使用data.rows作为结果');
                
                // 如果有columns属性但response没有，也使用它
                if (response.data.columns && !response.columns) {
                  response.columns = response.data.columns;
                  console.log('使用data.columns作为列信息');
                }
              } else if (response.data.results) {
                // 如果有results属性，使用它
                response.results = response.data.results;
                console.log('使用data.results作为结果');
                
                // 如果有columns属性但response没有，也使用它
                if (response.data.columns && !response.columns) {
                  response.columns = response.data.columns;
                  console.log('使用data.columns作为列信息');
                }
              } else {
                // 如果data是普通对象，可能每个属性都是一列
                const keys = Object.keys(response.data);
                if (keys.length > 0) {
                  // 尝试将对象转换为数组格式
                  try {
                    // 检查第一个属性是否为数组
                    const firstKey = keys[0];
                    if (Array.isArray(response.data[firstKey])) {
                      // 列式数据格式 (每列是一个数组)
                      const rowCount = response.data[firstKey].length;
                      const newResults = [];
                      
                      for (let i = 0; i < rowCount; i++) {
                        const row = {};
                        keys.forEach(key => {
                          row[key] = response.data[key][i];
                        });
                        newResults.push(row);
                      }
                      
                      response.results = newResults;
                      response.columns = keys;
                      console.log('将列式数据转换为行式数据');
                    } else {
                      // 单行数据 (每个属性是一个值)
                      response.results = [response.data];
                      response.columns = keys;
                      console.log('将单行对象数据转换为数组');
                    }
                  } catch (e) {
                    console.error('转换数据格式时出错:', e);
                  }
                }
              }
            }
            
            console.log('从data提取后的results长度:', response.results ? response.results.length : 0);
            console.log('从data提取后的columns:', response.columns);
          }
          
          // 如果仍然没有结果数据，创建一个空数组
          if (!response.results) {
            response.results = [];
            console.log('创建空结果数组');
          }
          
          // 如果没有列信息但有结果数据，从第一行提取列信息
          if ((!response.columns || response.columns.length === 0) && response.results.length > 0) {
            response.columns = Object.keys(response.results[0]);
            console.log('从第一行数据提取列信息:', response.columns);
          }
        
        // 创建一个模拟的自定义事件，传递结果
        console.log('====== 准备分发查询结果事件 ======');
        console.log('disableUIUpdate 值:', disableUIUpdate);
        console.log('response 数据:', response);
        console.log('props.tab.splitSqlStatements:', props.tab.splitSqlStatements);

        const customEvent = new CustomEvent('query-result', {
          detail: {
            ...response,
            tabId: queryParams.tabId,
            disableUIUpdate: disableUIUpdate,
            splitSqlStatements: queryParams.splitSqlStatements || props.tab.splitSqlStatements // 关键：用参数里的，如果没有就用props里的
          }
        });
        console.log('事件详情:', customEvent.detail);
        console.log('事件中的 disableUIUpdate:', customEvent.detail.disableUIUpdate);
        console.log('事件中的 splitSqlStatements:', customEvent.detail.splitSqlStatements);

        // 分发自定义事件
        document.dispatchEvent(customEvent);
      }).catch(error => {
        console.error('API直接查询失败:', error);
        cleanup();
        reject(error);
      });
    }).catch(importError => {
      console.error('导入executeQuery API函数失败:', importError);
      console.log('====== 走emit execute-query路径 ======');
      console.log('apiParams:', apiParams);
      emit('execute-query', apiParams);
    });
  });
};

// 处理数据源变化
const handleDatasourceChange = (value) => {
  // 如果当前正在执行查询，先重置状态
  isExecuting.value = false;
  
  // 更新本地选择
  selectedDatasourceId.value = value
  
  console.log(`数据源变更为: ${value}, 当前schema: ${selectedSchema.value}`);
  
  // 立即通知父组件数据源已经变更
  emit('tab-database-change', {
    tabId: props.tab.id,
    datasourceId: value,
    schema: selectedSchema.value
  })
  
  // fetchAndSetSchemas 会在 watch 中被调用，加载完schema后会再次通知父组件
  
  // 直接修改父组件中的tab对象，确保schema也被正确设置
  // 这是为了兼容可能的其他逻辑
  if (props.tab) {
    emit('update:tab', {
      ...props.tab,
      datasourceId: value,
      schema: selectedSchema.value
    })
  }
}

// 处理 SQL 内容变化
const handleSqlChange = (value) => {
  emit('update:sql', value)
}

// Helper to check for risky operations
const isRiskyOperation = (sql) => {
  if (!sql) return false;

  // 检查是否为多SQL语句
  const sqlStatements = sql.split(';').filter(stmt => stmt.trim().length > 0);

  if (sqlStatements.length > 1) {
    // 多SQL语句：只有当所有语句都是风险操作时才返回true
    // 如果包含SELECT等查询语句，则不走备份流程，而是走正常的多SQL执行流程
    return sqlStatements.every(stmt => {
      const lowerStmt = stmt.trim().toLowerCase();
      return lowerStmt.startsWith('delete') || lowerStmt.startsWith('update') || lowerStmt.startsWith('alter table');
    });
  } else {
    // 单SQL语句：检查是否为风险操作
    const lowerSql = sql.trim().toLowerCase();
    return lowerSql.startsWith('delete') || lowerSql.startsWith('update') || lowerSql.startsWith('alter table');
  }
};

// 获取编辑器中选中的文本
const getSelectedText = () => {
  if (!editorView) return null;
  
  const selection = editorView.state.selection;
  if (selection.main.empty) return null; // 没有选中文本
  
  const from = selection.main.from;
  const to = selection.main.to;
  const selectedText = editorView.state.doc.sliceString(from, to);
  return selectedText.trim();
};

// 处理执行查询
const handleExecuteQuery = async () => {
  console.log('====== handleExecuteQuery 开始 ======');
  console.log('当前sqlContent.value:', sqlContent.value);
  
  if (isExecuting.value) {
    ElMessage.warning('操作正在执行中，请稍候...');
    return;
  }
  
  // 检查是否有选中的文本
  const selectedSQL = editorView?.getSelection?.() || null;
  
  // 确定要执行的SQL（选中的或全部）
  const sql = selectedSQL || sqlContent.value.trim();
  
  if (!sql) {
    ElMessage.warning('请输入SQL查询语句');
    return;
  }
  
  if (!selectedDatasourceId.value) {
    ElMessage.warning('请选择数据源');
    return;
  }

  // 检查是否为ClickHouse数据源，且SQL是UPDATE语句
  if (isClickhouseDataSource() && sql.trim().toLowerCase().startsWith('update')) {
    ElMessage.error('ClickHouse数据源不支持UPDATE操作');
    return;
  }

  // 重置到第一页，但保留当前的pageSize设置
  currentPage.value = 1;
  
  // 记录SQL中的LIMIT值，但不改变分页大小
  const limitMatch = sql.match(/\blimit\s+(\d+)/i);
  if (limitMatch && limitMatch[1]) {
    const limitValue = parseInt(limitMatch[1]);
    if (!isNaN(limitValue) && limitValue > 0) {
      console.log(`检测到SQL中的LIMIT值: ${limitValue}，总数据量将被限制`);
    }
  }

  // 记录查询开始时间
  queryStartTime = Date.now();

  // 清空全局错误状态，确保新查询不受之前错误影响
  props.tab.error = null;

  // 发出执行状态变更事件
  emit('execution-state-change', true);
  
  // 如果是选中执行，添加日志并标记
  if (selectedSQL) {
    addExecutionLog(`执行选中的SQL: ${selectedSQL.length > 100 ? selectedSQL.substring(0, 100) + '...' : selectedSQL}`, 'pending');
    // 标记是选中执行，供后续处理
    props.tab.selectedSqlExecution = true;
    
    // 检查选中的SQL是否包含多个语句（以分号分隔）
    const sqlStatements = selectedSQL.split(';').filter(stmt => stmt.trim().length > 0);
    if (sqlStatements.length > 1) {
      console.log(`检测到选中执行的多条SQL语句: ${sqlStatements.length}条`);
      props.tab.isMultiStatement = true;
    } else {
      props.tab.isMultiStatement = false;
    }
  } else {
    props.tab.selectedSqlExecution = false;
    props.tab.isMultiStatement = false;
  }

  if (isRiskyOperation(sql)) {
    await prepareBackupAndConfirm(sql);
  } else {
    await proceedWithExecution(sql);
  }
}

const proceedWithExecution = async (sqlToExecute) => {
  console.log('====== proceedWithExecution 开始 ======');
  console.log('传入的sqlToExecute:', sqlToExecute);
  console.log('sqlToExecute包含分号数量:', (sqlToExecute.match(/;/g) || []).length);
  
  // 强制显示遮罩层并等待DOM更新
  emit('execution-state-change', true);
  await nextTick();

  try {
    // 保存原始SQL，避免修改用户的输入
    const originalSqlToExecute = sqlToExecute;
    
      // 如果是选中的SQL执行，保存到displaySql并标记是否为多SQL语句
  if (props.tab.selectedSqlExecution) {
    props.tab.displaySql = sqlToExecute;
    
    // 检查是否为多SQL语句（通过分号分隔）
    if (props.tab.isMultiStatement) {
      console.log('处理选中的多SQL语句执行');
      isMultiStatement.value = true;
    }
  }
    
    // 使用更安全的SQL分割逻辑，但不修改原始SQL的末尾分号
    // 只在内部处理时移除末尾分号，不影响编辑器内容
    let workingSql = sqlToExecute;
    
    // 更健壮的SQL分割逻辑，处理引号和转义字符
    const sqlStatements = [];
    let currentStatement = '';
    let inSingleQuote = false;
    let inDoubleQuote = false;
    let inBacktick = false;
    let escaped = false;

    for (let i = 0; i < workingSql.length; i++) {
      const char = workingSql[i];
      if (escaped) {
        currentStatement += char;
        escaped = false;
        continue;
      }
      if (char === '\\') {
        currentStatement += char;
        escaped = true;
        continue;
      }
      if (char === "'" && !inDoubleQuote && !inBacktick) {
        currentStatement += char;
        inSingleQuote = !inSingleQuote;
        continue;
      }
      if (char === '"' && !inSingleQuote && !inBacktick) {
        currentStatement += char;
        inDoubleQuote = !inDoubleQuote;
        continue;
      }
      if (char === '`' && !inSingleQuote && !inDoubleQuote) {
        currentStatement += char;
        inBacktick = !inBacktick;
        continue;
      }
      if (char === ';' && !inSingleQuote && !inDoubleQuote && !inBacktick) {
        if (currentStatement.trim()) {
          sqlStatements.push(currentStatement.trim());
        }
        currentStatement = '';
        continue;
      }
      currentStatement += char;
    }
    if (currentStatement.trim()) {
      sqlStatements.push(currentStatement.trim());
    }
    
    // 过滤掉空语句
    const filteredStatements = sqlStatements.filter(sql => sql.trim().length > 0);
    
    // 调试日志
    console.log(`SQL语句分割结果: 共${filteredStatements.length}条`);
    filteredStatements.forEach((sql, i) => {
      console.log(`SQL语句${i+1}: ${sql.substring(0, 100)}${sql.length > 100 ? '...' : ''}`);
    });
    console.log('完整的filteredStatements数组:', filteredStatements);

    // 根据语句数量选择不同处理路径
    if (filteredStatements.length > 1) {
      // --- 多SQL语句处理逻辑 ---
      isMultiStatement.value = true;
      // 不要在这里设置执行状态，而是在每条SQL执行时单独设置
    queryStartTime = Date.now();
      const totalStartTime = Date.now();

      // 保存分割后的SQL语句，供后续创建标签页时使用
      props.tab.splitSqlStatements = filteredStatements.slice(); // 创建副本
      console.log('保存分割后的SQL语句:', props.tab.splitSqlStatements);
      console.log('分割后的SQL语句数量:', props.tab.splitSqlStatements.length);
      // 确保每个SQL语句都被正确保存
      props.tab.splitSqlStatements.forEach((sql, index) => {
        console.log(`保存的SQL语句${index + 1}:`, sql.substring(0, 100) + (sql.length > 100 ? '...' : ''));
      });

      // 清空主标签页和额外标签页的旧结果
      props.tab.results = [];
      props.tab.columns = [];
      props.tab.resultTabs = []; // 修复：应该修改props.tab.resultTabs而不是resultTabs.value
      emit('update:tab', {...props.tab});

      

      let successCount = 0;
      let errorCount = 0;
      
      const allResults = [];

      // 初始化主标签页和额外标签页
      showMainResultTab.value = true;
      props.tab.executed = false;
      emit('update:tab', {...props.tab});
    await nextTick();

      // 一次性发送所有SQL语句到后端
      // 确保每条SQL语句都以分号结尾
      const formattedStatements = filteredStatements.map(sql => {
        const trimmed = sql.trim();
        return trimmed.endsWith(';') ? trimmed : trimmed + ';';
      });
      // 最后一条SQL不需要分号
      if (formattedStatements.length > 0) {
        const lastIndex = formattedStatements.length - 1;
        if (formattedStatements[lastIndex].endsWith(';')) {
          formattedStatements[lastIndex] = formattedStatements[lastIndex].slice(0, -1);
        }
      }
      const combinedSql = formattedStatements.join(';');
      addExecutionLog(`执行SQL: ${combinedSql}`, 'pending');
      
      // 显示执行动画
    emit('execution-state-change', true);
      isExecuting.value = true;
      await nextTick(); // 确保动画显示
      
      try {
        // console.log(`一次性执行多SQL语句: ${combinedSql}`);
        
        // 发送到后端执行
        console.log('====== 调用 handleExecuteQueryDirectly (一次性执行多SQL) ======');
        const result = await handleExecuteQueryDirectly({
          tabId: props.tab.id,
          sql: combinedSql,
          datasourceId: selectedDatasourceId.value,
          schema: selectedSchema.value,
          tableName: extractTableName(filteredStatements[0]),
          isMultiStatement: true,
          splitSqlStatements: filteredStatements.slice() // 关键：传递分割后的SQL
        }, false);
        
        // 确保结果中包含分割后的SQL语句
        if (result && !result.splitSqlStatements) {
          result.splitSqlStatements = filteredStatements.slice();
          console.log('在结果中添加分割后的SQL语句:', result.splitSqlStatements);
        }
        
        // 检查是否有多结果集数据
        if (result.is_multi_statement && result.all_results && Array.isArray(result.all_results)) {
          console.log(`后端返回了${result.all_results.length}个结果集`);
          console.log(`各结果集行数: ${result.all_results.map(r => r.rows ? r.rows.length : 0)}`);
          console.log(`各结果集列数: ${result.all_results.map(r => r.columns ? r.columns.length : 0)}`);
          console.log('后端返回的result.sqls:', result.sqls);
          console.log('后端返回的完整result对象:', result);
          console.log('props.tab.sql内容:', props.tab.sql);

          // 第一个结果放在主标签页
          if (result.all_results.length > 0) {
            const firstResult = result.all_results[0];
            props.tab.results = firstResult.rows || [];
            props.tab.columns = firstResult.columns || [];

            // 不在主标签页显示错误，错误信息会在对应的结果标签页中显示
            props.tab.messages = '';
            props.tab.error = null;

            props.tab.rowCount = firstResult.affected_rows || (firstResult.rows ? firstResult.rows.length : 0);
            props.tab.executed = true;
            // 修正：只显示第一个SQL
            if (result.sqls && result.sqls[0]) {
              console.log('使用后端返回的第一个SQL:', result.sqls[0]);
              props.tab.displaySql = result.sqls[0];
            } else if (props.tab.sql && typeof props.tab.sql === 'string') {
              // 尝试分割props.tab.sql
              const sqlArr = props.tab.sql.split(';').map(s => s.trim()).filter(Boolean);
              const firstSql = sqlArr[0] ? (sqlArr[0].endsWith(';') ? sqlArr[0] : sqlArr[0] + ';') : props.tab.sql;
              console.log('后端未返回sqls，使用分割后的第一个SQL:', firstSql);
              props.tab.displaySql = firstSql;
            } else {
              console.log('无法获取第一个SQL，使用空字符串');
              props.tab.displaySql = '';
            }
            emit('update:tab', {...props.tab});
            
            // 清空之前的额外标签页
            props.tab.resultTabs = []; // 修复：应该修改props.tab.resultTabs而不是resultTabs.value
            
            // 处理额外的结果集
            for (let i = 1; i < result.all_results.length; i++) {
              const resultSet = result.all_results[i];

              // 获取对应的SQL语句
              let sql = `SQL结果集 ${i+1}`;
              if (result.sqls && result.sqls[i]) {
                sql = result.sqls[i];
                console.log(`使用后端返回的第 ${i+1} 个SQL:`, sql);
              } else if (props.tab.splitSqlStatements && props.tab.splitSqlStatements[i]) {
                // 使用保存的分割后的SQL语句
                sql = props.tab.splitSqlStatements[i];
                console.log(`使用保存的分割后的第 ${i+1} 个SQL:`, sql);
              } else {
                // 最后的回退选项
                sql = `SQL语句 ${i+1}`;
                console.log(`无法获取具体SQL，使用通用标识: ${sql}`);
              }

              console.log(`[DEBUG] 标签页 ${i+1} 的SQL内容:`, sql);

              console.log(`处理第 ${i+1} 个结果集:`, resultSet);

          const tabInfo = {
                id: `result-tab-${i}`,
                sql: sql,
            results: resultSet.rows || [],
            columns: resultSet.columns || [],
                messages: '',
                error: null,
                rowCount: resultSet.affected_rows || (resultSet.rows ? resultSet.rows.length : 0),
                affected_rows: resultSet.affected_rows, // 新增
            currentPage: 1,
            pageSize: 20,
                executed: true,
                isSelect: isSelectQuery(sql) // 使用SQL语句来准确判断是否为SELECT查询
              };
              // console.log(' 查询执行影响行数:', resultTab.affected_rows);

              // 添加到结果标签页列表
              if (!props.tab.resultTabs) {
                props.tab.resultTabs = [];
              }
              props.tab.resultTabs.push(tabInfo); // 修复：应该修改props.tab.resultTabs而不是resultTabs.value

              // 初始化新标签页的编辑模式状态
              const newTabIndex = props.tab.resultTabs.length - 1;
              resultTabsEditMode.value[newTabIndex] = false; // 始终默认为只读模式
              console.log(`初始化新标签页 ${tabInfo.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

              // 确保resultTableRefs数组长度与resultTabs同步
              while (resultTableRefs.value.length < props.tab.resultTabs.length) {
                resultTableRefs.value.push(undefined);
              }
              
              console.log(`已添加标签页: ${tabInfo.id}, 行数=${tabInfo.results.length}`);
            }
            
            // 显示主标签页和额外标签页
            showMainResultTab.value = true;

            // 如果有额外的结果标签页，激活最后一个（通常是SELECT查询）
            if (props.tab.resultTabs && props.tab.resultTabs.length > 0) {
              const lastTabIndex = props.tab.resultTabs.length - 1;
              const lastTabId = props.tab.resultTabs[lastTabIndex].id;
              activeResultTab.value = lastTabId;
              console.log(`已激活最后一个标签页: ${activeResultTab.value}, 共有 ${props.tab.resultTabs.length} 个额外标签页`);
            } else {
              activeResultTab.value = 'result';
              console.log(`已激活主标签页，共有 ${props.tab.resultTabs ? props.tab.resultTabs.length : 0} 个额外标签页`);
            }

            // 确保更新标签页状态
            emit('update:tab', {...props.tab});

            // 等待DOM更新，确保标签页正确显示
            await nextTick();

            // 成功执行所有SQL
            const execTime = Date.now() - queryStartTime;
            updateSqlExecutionResult(`在 ${execTime} ms 内完成，返回 ${props.tab.rowCount} 行数据`, 'success');

            // 关闭执行动画
            emit('execution-state-change', false);
            isExecuting.value = false;
          }
        } else {
          // 关闭执行动画
          emit('execution-state-change', false);
          isExecuting.value = false;

          // 保存分割后的SQL语句，供后续创建标签页时使用
                const localSplitSqlStatements = filteredStatements.slice(); // 创建本地副本
      props.tab.splitSqlStatements = localSplitSqlStatements; // 创建副本
      console.log('保存分割后的SQL语句:', localSplitSqlStatements);
      console.log('分割后的SQL语句数量:', localSplitSqlStatements.length);
      // 确保每个SQL语句都被正确保存
      localSplitSqlStatements.forEach((sql, index) => {
        console.log(`保存的SQL语句${index + 1}:`, sql.substring(0, 100) + (sql.length > 100 ? '...' : ''));
      });

          // 使用原来的逻辑，逐条执行SQL
          for (let i = 0; i < filteredStatements.length; i++) {
            const sql = filteredStatements[i].trim();
            if (!sql) continue;
            
            // 检查是否是SELECT语句，无论如何都需要创建Result标签页
            const isSelectQuery = sql.trim().toLowerCase().startsWith('select');
            
            // 显示执行动画 - 每条SQL执行前都需要显示动画
            emit('execution-state-change', true);
            isExecuting.value = true;
            await nextTick(); // 确保动画显示
            
            addExecutionLog(`SQL ${i + 1}/${filteredStatements.length}: ${sql}`, 'pending');
            const sqlStartTime = Date.now();
            const tableName = extractTableName(sql);

            try {
              // 确保每条SQL单独执行，不包含分号
              const cleanSql = sql.endsWith(';') ? sql.slice(0, -1) : sql;
              
              console.log(`执行单条SQL语句 ${i+1}/${filteredStatements.length}: ${cleanSql}`);
              
              // 每条SQL单独发送到后端执行
              console.log('====== 调用 handleExecuteQueryDirectly (单条SQL执行) ======');
              const result = await handleExecuteQueryDirectly({
                tabId: props.tab.id,
                sql: cleanSql, // 使用清理后的SQL
                datasourceId: selectedDatasourceId.value,
                schema: selectedSchema.value,
                tableName,
                isSingleStatement: true, // 标记为单条语句执行
                splitSqlStatements: localSplitSqlStatements.slice() // 传递分割后的SQL语句
              }, true); // 传入true以禁用UI更新
              
              // 确保结果中包含分割后的SQL语句
              if (result && !result.splitSqlStatements) {
                result.splitSqlStatements = localSplitSqlStatements.slice();
                console.log('在单条SQL执行结果中添加分割后的SQL语句:', result.splitSqlStatements);
              }
              
              // 添加isSelectQuery标记，无论结果是否为空都需要创建Result标签页
              allResults.push({ sql, result, hasError: false, isSelectQuery });
              successCount++;
              const execTime = Date.now() - sqlStartTime;

              let resultMessage = `在 ${execTime} ms 内完成`;
              if (result && result.results && result.results.length > 0) {
                  resultMessage += `，返回 ${result.results.length} 行数据`;
              } else if (isDML(sql)) {
                  resultMessage += `，${result?.messages || (result?.affected_rows !== undefined ? `${result.affected_rows} 行受影响` : '操作成功')}`;
              } else if (isDDL(sql)) {
                  resultMessage += `，${result?.messages || '操作成功'}`;
              } else {
                  resultMessage += '，未返回数据';
              }
              updateSqlExecutionResult(resultMessage, 'success');
              
              // 关闭执行动画
              emit('execution-state-change', false);
              isExecuting.value = false;
    await nextTick();
              
              if (i === 0) {
                // 第一条SQL语句结果放在主标签页
                props.tab.results = result.results || [];
                props.tab.columns = result.columns || [];
                props.tab.messages = result.messages || '';
                props.tab.error = null;
                props.tab.rowCount = result.affected_rows !== undefined ? result.affected_rows : (result.results ? result.results.length : 0);
        props.tab.executed = true;
                props.tab.displaySql = sql;
                emit('update:tab', {...props.tab});
                
                // 激活主标签页
                activeResultTab.value = 'result';
              } else {
                // 后续SQL语句结果创建额外的标签页
                console.log(`[DEBUG] 成功标签页 ${i+1} 的SQL内容:`, sql);
                const tabInfo = {
                  id: `result-tab-${i}`,
                  sql: localSplitSqlStatements[i] || sql || `[错误] 无法获取第${i+1}条SQL语句`,
                  results: result.results || [],
                  columns: result.columns || [],
                  messages: result.messages || '',
                  error: null,
                  rowCount: result.affected_rows !== undefined ? result.affected_rows : (result.results ? result.results.length : 0),
                  affected_rows: result.affected_rows, // 新增
                  currentPage: 1,
                  pageSize: 20,
                  splitSqlStatements: localSplitSqlStatements.slice(), // 保存分割后的SQL语句
                  executed: true,
                  isSelect: isSelectQuery
                };
                
                // 添加到结果标签页列表
                if (!props.tab.resultTabs) {
                  props.tab.resultTabs = [];
                }
                props.tab.resultTabs.push(tabInfo); // 修复：应该修改props.tab.resultTabs而不是resultTabs.value

                // 初始化新标签页的编辑模式状态
                const newTabIndex = props.tab.resultTabs.length - 1;
                resultTabsEditMode.value[newTabIndex] = false; // 始终默认为只读模式
                console.log(`初始化新标签页 ${tabInfo.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

                // 确保resultTableRefs数组长度与resultTabs同步
                while (resultTableRefs.value.length < props.tab.resultTabs.length) {
                  resultTableRefs.value.push(undefined);
                }
                
                // 激活新创建的标签页
                activeResultTab.value = tabInfo.id;
                console.log(`已激活标签页: ${activeResultTab.value}, SQL ${i+1}/${filteredStatements.length}`);
              }
              
              // 等待DOM更新，确保标签页显示
        await nextTick();

              // 如果不是最后一条SQL，暂停一小段时间让用户查看结果
              if (i < filteredStatements.length - 1) {
                // 延长暂停时间，确保用户能看到结果
                await new Promise(resolve => setTimeout(resolve, 800));
      }
    } catch (error) {
              // 同样添加isSelectQuery标记
              allResults.push({ sql, result: { error: error.message || '未知错误', messages: error.message || '执行失败' }, hasError: true, isSelectQuery });
              errorCount++;
              updateSqlExecutionResult(`执行失败: ${error.message || '未知错误'}`, 'error');
              
              // 关闭执行动画
              emit('execution-state-change', false);
              isExecuting.value = false;
              await nextTick();
              
              // 无论是第几条SQL语句失败，都创建错误结果标签页
              console.log(`====== 创建第 ${i + 1} 条SQL的错误结果标签页 ======`);
              console.log('错误信息:', error.message || '未知错误');

              // 检查是否达到标签页上限（10个）
              if (props.tab.resultTabs.length >= 10) {
                console.log('已达到标签页上限(10)，清空所有标签页并从result 1重新开始');
                props.tab.resultTabs = [];
              }

              // 获取SQL文本用于标签页显示
              console.log(`[DEBUG] 错误标签页 ${i+1} 的原始SQL:`, sql);
              console.log(`[DEBUG] filteredStatements[${i}]:`, filteredStatements[i]);

              // 确保使用正确的单条SQL
              let displaySql = localSplitSqlStatements[i] || sql || `[错误] 无法获取第${i+1}条SQL语句`;
              console.log(`[DEBUG] 使用本地分割后的SQL:`, displaySql);
              console.log(`[DEBUG] localSplitSqlStatements[${i}]:`, localSplitSqlStatements[i]);
              console.log(`[DEBUG] 原始sql:`, sql);

              const sqlLabel = displaySql.length > 50 ? displaySql.substring(0, 50) + '...' : displaySql;

              // 创建错误结果标签页
              const errorResultTab = {
                id: `result-tab-${Date.now()}-${i}`,
                sql: displaySql, // 使用完整的单条SQL，而不是截断的sqlLabel
                results: [],
                columns: [],
                timestamp: Date.now(),
                currentPage: 1,
                pageSize: 20,
                executed: true,
                error: error.message || '未知错误',
                messages: '执行失败',
                rowCount: 0,
                affected_rows: 0,
                isDmlResult: false,
                splitSqlStatements: localSplitSqlStatements.slice() // 保存分割后的SQL语句
              };

              console.log(`新创建的第 ${i + 1} 条SQL错误结果标签页:`, errorResultTab);

              // 添加到结果标签页数组
              props.tab.resultTabs.push(errorResultTab);

              // 初始化新标签页的编辑模式状态
              const newTabIndex = props.tab.resultTabs.length - 1;
              resultTabsEditMode.value[newTabIndex] = false;
              console.log(`初始化错误标签页 ${errorResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

              // 切换到新创建的错误标签页
              activeResultTab.value = errorResultTab.id;
              console.log('切换到错误标签页:', activeResultTab.value);

              if (i === 0) {
                // 第一条SQL语句执行失败，不在主标签页显示错误，错误信息会在对应的结果标签页中显示
                props.tab.results = [];
                props.tab.columns = [];
                props.tab.messages = '';
                props.tab.error = null;
                props.tab.rowCount = 0;
                props.tab.executed = true;
                props.tab.displaySql = sql;
              }

              // 更新父组件tab对象
              emit('update:tab', {...props.tab});

              // 等待DOM更新，确保标签页显示
              await nextTick();

              // 如果不是最后一条SQL，暂停一小段时间让用户查看结果
              if (i < filteredStatements.length - 1) {
                // 延长暂停时间，确保用户能看到结果
                await new Promise(resolve => setTimeout(resolve, 800));
              }
            }
          }

          // 逐条执行完成后，确保更新标签页状态
          emit('update:tab', {...props.tab});
          console.log(`逐条执行完成，共创建了 ${props.tab.resultTabs ? props.tab.resultTabs.length : 0} 个额外标签页`);
        }
      } catch (error) {
        // 处理一次性执行多SQL语句时的错误
        console.error('多SQL语句执行失败:', error);
        // ElMessage.error(`多SQL语句执行失败: ${error.message || '未知错误'}`);
        
        // 关闭执行动画
        emit('execution-state-change', false);
        isExecuting.value = false;
        
        // 不在主标签页显示错误信息，错误信息会在对应的结果标签页中显示
        props.tab.results = [];
        props.tab.columns = [];
        props.tab.messages = '';
        props.tab.error = null;
        props.tab.rowCount = 0;
        props.tab.executed = true;
        props.tab.displaySql = combinedSql;
        emit('update:tab', {...props.tab});
        
        // 激活主标签页
        activeResultTab.value = 'result';
        showMainResultTab.value = true;
      }
    } else {
      // --- 单SQL语句处理逻辑 ---
      const tableName = extractTableName(sqlToExecute);
      addExecutionLog(`${sqlToExecute}`, 'pending');
      
      emit('execution-state-change', true);
      queryStartTime = Date.now();

      const page = currentPage.value;
      const pageSizeValue = pageSize.value;
      
      try {
        // 标记是否为SELECT查询，无论结果是否为空都显示结果标签页
        const isSelectQuery = sqlToExecute.trim().toLowerCase().startsWith('select');
        
        // 重要：提取表名并保存到标签页，确保后续备份SQL能正确使用
        const extractedTableName = extractTableName(sqlToExecute);
        if (extractedTableName) {
          props.tab.currentQueryTableName = extractedTableName;
          console.log('执行SQL前已设置当前表名:', extractedTableName);
        }
        
        // 确保SQL语句不以分号结尾，避免MySQL多语句执行问题
        const cleanSql = sqlToExecute.endsWith(';') ? sqlToExecute.slice(0, -1) : sqlToExecute;
        console.log('清理后的SQL:', cleanSql);
        
      console.log('====== 调用 handleExecuteQueryDirectly (普通执行) ======');
      const result = await handleExecuteQueryDirectly({
        tabId: props.tab.id,
          sql: cleanSql, // 使用清理后的SQL
        datasourceId: selectedDatasourceId.value,
        schema: selectedSchema.value,
          tableName: extractedTableName || tableName  // 优先使用提取的表名
        }, false); // 传入false以允许UI更新

      // 每次单SQL执行都创建新的结果标签页，不更新主标签页
      if (result) {
        console.log('单SQL语句执行结果:', result);

        // 检查是否达到标签页上限（10个）
        if (props.tab.resultTabs.length >= 10) {
          console.log('已达到标签页上限(10)，清空所有标签页并从result 1重新开始');
          props.tab.resultTabs = [];
        }

        // 检查是否有多结果集数据
        if (result.is_multi_statement && result.all_results && Array.isArray(result.all_results) && result.all_results.length > 0) {
          console.log('检测到单SQL语句执行返回了多结果集数据:', result.all_results);
          console.log(`多结果集数量: ${result.all_results.length}`);
          console.log(`各结果集行数: ${result.all_results.map(r => r.rows ? r.rows.length : 0)}`);
          console.log(`各结果集列数: ${result.all_results.map(r => r.columns ? r.columns.length : 0)}`);

          // 为每个结果集创建独立的结果标签页
          for (let i = 0; i < result.all_results.length; i++) {
            const resultSet = result.all_results[i];

            // 获取SQL文本用于标签页显示
            const sqlText = sqlToExecute || props.tab.sql || '查询';
            const sqlLabel = sqlText.length > 50 ? sqlText.substring(0, 50) + '...' : sqlText;

            // 创建新的结果标签页
            const newResultTab = {
              id: `result-tab-${Date.now()}-${i}`,
              sql: result.all_results.length > 1 ? `${sqlLabel} - 结果集 ${i+1}` : sqlLabel,
              results: resultSet.rows || [],
              columns: resultSet.columns || [],
              timestamp: Date.now(),
              currentPage: 1,
              pageSize: 20,
              executed: true,
              error: null,
              messages: '',
              rowCount: resultSet.affected_rows !== undefined ? resultSet.affected_rows : (resultSet.rows ? resultSet.rows.length : 0),
              affected_rows: resultSet.affected_rows,
              isDmlResult: false
            };

            console.log(`新创建的结果标签页 ${i+1}:`, newResultTab);

            // 添加到结果标签页数组
            props.tab.resultTabs.push(newResultTab);

            // 初始化新标签页的编辑模式状态
            const newTabIndex = props.tab.resultTabs.length - 1;
            resultTabsEditMode.value[newTabIndex] = false;
            console.log(`初始化新标签页 ${newResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);
          }

          // 切换到最后创建的标签页
          if (props.tab.resultTabs.length > 0) {
            activeResultTab.value = props.tab.resultTabs[props.tab.resultTabs.length - 1].id;
            console.log('切换到新标签页:', activeResultTab.value);
          }

          // 不更新主标签页的内容，保持之前的状态
          // props.tab.results = firstResult.rows || [];  // 注释掉
          // props.tab.columns = firstResult.columns || [];  // 注释掉
          // props.tab.rowCount = firstResult.affected_rows || (firstResult.rows ? firstResult.rows.length : 0);  // 注释掉
          // props.tab.executed = true;  // 注释掉
          // props.tab.displaySql = sqlToExecute;  // 注释掉

          // 确保显示结果标签页
          showMainResultTab.value = true;
        } else {
          // 普通单结果集处理 - 创建新的结果标签页
          console.log('处理普通单结果集:', result);

          // 获取SQL文本用于标签页显示
          const sqlText = sqlToExecute || props.tab.sql || '查询';
          const sqlLabel = sqlText.length > 50 ? sqlText.substring(0, 50) + '...' : sqlText;

          // 创建新的结果标签页
          const newResultTab = {
            id: `result-tab-${Date.now()}`,
            sql: sqlLabel,
            results: result.results || [],
            columns: result.columns || [],
            timestamp: Date.now(),
            currentPage: 1,
            pageSize: 20,
            executed: true,
            error: null,
            messages: result.messages || '',
            rowCount: result.affected_rows !== undefined ? result.affected_rows : (result.results ? result.results.length : 0),
            affected_rows: result.affected_rows,
            isDmlResult: false
          };

          console.log('新创建的单结果集标签页:', newResultTab);

          // 添加到结果标签页数组
          props.tab.resultTabs.push(newResultTab);

          // 初始化新标签页的编辑模式状态
          const newTabIndex = props.tab.resultTabs.length - 1;
          resultTabsEditMode.value[newTabIndex] = false;
          console.log(`初始化新标签页 ${newResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

          // 不更新主标签页的内容，保持之前的状态
          // props.tab.results = result.results || [];  // 注释掉
          // props.tab.columns = result.columns || [];  // 注释掉
          // props.tab.messages = result.messages || '';  // 注释掉
          // props.tab.error = null;  // 注释掉
          // props.tab.rowCount = result.affected_rows !== undefined ? result.affected_rows : (result.results ? result.results.length : 0);  // 注释掉
          // props.tab.executed = true;  // 注释掉
          // props.tab.displaySql = sqlToExecute;  // 注释掉
          // emit('update:tab', {...props.tab});  // 注释掉
        }
        
        // 激活最后一个结果标签页
        if (props.tab.resultTabs && props.tab.resultTabs.length > 0) {
          const lastTab = props.tab.resultTabs[props.tab.resultTabs.length - 1];
          activeResultTab.value = lastTab.id;
          console.log(`[proceedWithExecution] 切换到最后一个结果标签页: ${activeResultTab.value}`);
        }
        // 确保显示结果标签页
        showMainResultTab.value = true;
      }
        
      // 结果由 handleQueryResult 事件处理器处理
        await nextTick();
      } catch (error) {
        // 不在主标签页显示错误信息，错误信息会在对应的结果标签页中显示
        // 不清空主标签页的结果，保持之前的查询结果
        // props.tab.results = [];  // 注释掉，保持之前的结果
        // props.tab.columns = [];  // 注释掉，保持之前的结果
        props.tab.messages = '';
        props.tab.error = null;
        // props.tab.rowCount = 0;  // 注释掉，保持之前的行数
        // props.tab.executed = true;  // 注释掉，保持之前的执行状态
        // props.tab.displaySql = sqlToExecute;  // 注释掉，保持之前的SQL

        // 创建错误结果标签页
        console.log('====== 创建错误结果标签页 ======');
        console.log('错误信息:', error.message || '未知错误');

        // 检查是否达到标签页上限（10个）
        if (props.tab.resultTabs.length >= 10) {
          console.log('已达到标签页上限(10)，清空所有标签页并从result 1重新开始');
          props.tab.resultTabs = [];
        }

        // 获取SQL文本用于标签页显示
        const sqlText = sqlToExecute || props.tab.sql || '查询';
        const sqlLabel = sqlText.length > 50 ? sqlText.substring(0, 50) + '...' : sqlText;

        // 创建错误结果标签页
        const errorResultTab = {
          id: `result-tab-${Date.now()}`,
          sql: sqlLabel,
          results: [],
          columns: [],
          timestamp: Date.now(),
          currentPage: 1,
          pageSize: 20,
          executed: true,
          error: error.message || '未知错误',
          messages: '执行失败',
          rowCount: 0,
          affected_rows: 0,
          isDmlResult: false
        };

        console.log('新创建的错误结果标签页:', errorResultTab);

        // 添加到结果标签页数组
        props.tab.resultTabs.push(errorResultTab);

        // 初始化新标签页的编辑模式状态
        const newTabIndex = props.tab.resultTabs.length - 1;
        resultTabsEditMode.value[newTabIndex] = false;
        console.log(`初始化错误标签页 ${errorResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

        // 切换到新创建的错误标签页
        activeResultTab.value = errorResultTab.id;
        console.log('切换到错误标签页:', activeResultTab.value);

        // 确保显示结果标签页
        showMainResultTab.value = true;

        emit('update:tab', {...props.tab});

        // 记录错误日志
        console.error('单SQL执行出错:', error);
        addExecutionLog(`执行失败: ${error.message || '未知错误'}`, 'error');
      }
    }

    // 重置执行状态
      resetExecutingState();
  } catch (error) {
    // 出错时关闭加载提示
    console.error('执行过程出错:', error);
    // ElMessage.error(`执行失败: ${error.message || '未知错误'}`);
    resetExecutingState();
  }
}

const generateReverseUpdateSql = (sql) => {
  // Regex for simple UPDATE: UPDATE table SET col = val1 WHERE col = val2;
  const simpleUpdateRegex = /^UPDATE\s+([\w`"]+)\s+SET\s+([\w`"]+)\s*=\s*([^,]+?)\s+WHERE\s+\2\s*=\s*([^;]+?);?$/i;
  const match = sql.trim().match(simpleUpdateRegex);

  if (match) {
    const [, tableName, columnName, newValue, oldValue] = match;
    const trimmedNewValue = newValue.trim();
    const trimmedOldValue = oldValue.trim();
    return `UPDATE ${tableName} SET ${columnName} = ${trimmedOldValue} WHERE ${columnName} = ${trimmedNewValue};`;
  }
  return null;
};

// 添加一个新的函数，用于在新的标签页中执行备份查询并获取结果
const executeBackupQueryInBackground = async (selectSql, tableName, originalSql) => {
  console.log('===== 开始备份过程 =====');
  console.log('备份SQL:', selectSql);
  console.log('表名:', tableName);
  console.log('原始SQL:', originalSql);
  console.log('tabId:', props.tab.id);
  console.log('datasourceId:', selectedDatasourceId.value);
  console.log('schema:', selectedSchema.value);
  
  try {
    // 显示加载提示
    const backupLoading = ElLoading.service({
      lock: true,
      text: '正在生成备份数据...',
      background: 'rgba(0, 0, 0, 0.7)',
    });
    
    // 添加超时保护 - 增加到60秒
    console.log('设置超时保护 (60秒)...');
    let timeoutId = setTimeout(() => {
      backupLoading.close();
      console.error('备份数据操作超时 - 60秒限制到达');
      
      // 显示确认对话框，让用户选择是否继续
      ElMessageBox.confirm(
        '备份数据操作超时，可能是因为数据量较大。是否跳过备份，直接执行原始操作？',
        '备份超时',
        {
          confirmButtonText: '继续执行',
          cancelButtonText: '取消操作',
          type: 'warning',
        }
      ).then(() => {
        proceedWithExecution(originalSql);
      }).catch(() => {
        ElMessage.info('操作已取消');
        // 用户取消超时操作时也要重置执行状态
        resetExecutingState();
      });
    }, 60000);
    
    // 使用组件自身的查询方法
    try {
      console.log('准备执行备份查询...');
      // 构建查询参数
      const queryParams = {
        tabId: props.tab.id,
        sql: selectSql,
        datasourceId: selectedDatasourceId.value,
        schema: selectedSchema.value
      };
      
      console.log('备份查询参数:', JSON.stringify(queryParams));
      
      // 执行查询
      console.log('====== 调用 handleExecuteQueryDirectly (备份查询，禁用UI更新) ======');
      console.log('正在执行备份查询...');
      const result = await handleExecuteQueryDirectly(queryParams, true); // 传入true以禁用UI更新
      
      // 清除超时
      console.log('清除超时定时器');
      clearTimeout(timeoutId);
      backupLoading.close();

      // 重置执行状态，因为备份查询已完成
      console.log('备份查询完成，重置执行状态');
      resetExecutingState();
      
      // 检查结果
      console.log('检查查询结果:', result ? '结果不为空' : '结果为空');
      if (result) {
        console.log('结果详情:', 
          '有结果集?', !!result.results, 
          '结果行数:', result.results ? result.results.length : 0
        );
      }
      
      if (result && result.results && result.results.length > 0) {
        console.log('备份查询返回了有效数据，行数:', result.results.length);
        console.log('前3行数据示例:', JSON.stringify(result.results.slice(0, 3)));
        
        // 生成INSERT语句
        console.log('开始生成INSERT备份语句...');
        const insertSql = generateInsertSql(tableName, result.results);
        console.log('INSERT备份语句生成结果:', insertSql ? '成功' : '失败');
        if (insertSql) {
          console.log('生成的INSERT语句长度:', insertSql.length);
          
          // 显示INSERT备份语句对话框
          console.log('准备显示备份SQL对话框');
          backupSqlForDialog.value = insertSql;
          originalSqlToExecute.value = originalSql;
          backupDialogVisible.value = true;
          
          // 记录日志
          console.log('添加执行日志');
          addExecutionLog(`已生成备份INSERT语句，影响 ${result.results.length} 行数据`, 'success');
          console.log('===== 备份过程完成 =====');
          return true;
        } else {
          console.error('无法生成有效的INSERT语句');
        }
      }
      
      // 如果没有数据或无法生成INSERT
      console.log('显示确认对话框，询问是否继续执行');
      ElMessageBox.confirm(
        '查询未返回任何数据或无法生成备份语句。是否继续执行原始操作？',
        '确认',
        {
          confirmButtonText: '继续执行',
          cancelButtonText: '取消',
          type: 'info',
        }
      ).then(() => {
        console.log('用户选择继续执行原始操作');
        proceedWithExecution(originalSql);
      }).catch(() => {
        console.log('用户取消了操作');
        ElMessage.info('操作已取消');
        // 用户取消操作时也要重置执行状态
        resetExecutingState();
      });
      
      return false;
    } catch (error) {
      // 清除超时
      clearTimeout(timeoutId);
      backupLoading.close();
      
      console.error('备份查询执行失败:', error);
      console.error('错误详情:', {
        message: error.message || '未知错误',
        stack: error.stack,
        name: error.name
      });
      
      ElMessageBox.confirm(
        `备份查询失败: ${error.message || '未知错误'}。是否继续执行原始操作？`,
        '错误',
        {
          confirmButtonText: '继续执行',
          cancelButtonText: '取消',
          type: 'warning',
        }
      ).then(() => {
        proceedWithExecution(originalSql);
      }).catch(() => {
        ElMessage.info('操作已取消');
        // 用户取消操作时也要重置执行状态
        resetExecutingState();
      });
      
      return false;
    }
  } catch (outerError) {
    console.error('备份过程出现异常:', outerError);
    console.error('异常详情:', {
      message: outerError.message || '未知错误',
      stack: outerError.stack,
      name: outerError.name
    });
    ElMessage.error(`备份过程出错: ${outerError.message}`);
    // 备份过程出错时也要重置执行状态
    resetExecutingState();
    return false;
  }
};

const prepareBackupAndConfirm = async (sql) => {
  const loading = ElLoading.service({
    lock: true,
    text: '正在分析语句...',
    background: 'rgba(0, 0, 0, 0.7)',
  });

  try {
    // 获取SQL语句的大写形式，用于关键字检测
    const sqlUpper = sql.trim().toUpperCase();
    
    // 直接通过关键字检测操作类型
    let operationType = null;
    let tableName = '';
    let selectSql = '';
    
    // 使用我们改进的函数来提取表名
    tableName = extractTableName(sql);
    
    // 如果提取失败，使用备用方法
    if (!tableName) {
      // 提取表名的简单正则
      const tableNameRegex = /(?:UPDATE|DELETE\s+FROM|ALTER\s+TABLE)\s+[`"']?([a-zA-Z0-9_]+)[`"']?/i;
      const tableMatch = sql.match(tableNameRegex);
      if (tableMatch) {
        tableName = tableMatch[1];
      } else {
        // 尝试其他方式提取表名
        const fallbackRegex = /\s+([a-zA-Z0-9_]+)\s+/i;
        const fallbackMatch = sql.match(fallbackRegex);
        if (fallbackMatch) {
          tableName = fallbackMatch[1];
        }
      }
    }
    
    // 根据关键字确定操作类型并生成查询SQL
    if (sqlUpper.startsWith('UPDATE')) {
      operationType = 'UPDATE';
      const whereClause = sqlUpper.includes('WHERE') ? 
        sql.substring(sql.toUpperCase().indexOf('WHERE')) : '';
      selectSql = `SELECT * FROM ${tableName} ${whereClause}`;
    } else if (sqlUpper.startsWith('DELETE')) {
      operationType = 'DELETE';
      const whereClause = sqlUpper.includes('WHERE') ? 
        sql.substring(sql.toUpperCase().indexOf('WHERE')) : '';
      selectSql = `SELECT * FROM ${tableName} ${whereClause}`;
    } else if (sqlUpper.startsWith('ALTER')) {
      operationType = 'ALTER';
      selectSql = `SELECT * FROM ${tableName}`;
    }
    
    // 关闭加载动画
    loading.close();

    // 如果是需要备份的操作类型
    if (operationType) {
      // 根据操作类型显示不同的确认信息
      let confirmMessage = '';
      if (operationType === 'UPDATE') {
        confirmMessage = '检测到UPDATE操作。是否先备份将要修改的数据？';
      } else if (operationType === 'DELETE') {
        confirmMessage = '检测到DELETE操作。是否先备份将要删除的数据？';
      } else if (operationType === 'ALTER') {
        confirmMessage = '检测到ALTER操作。是否先备份整个表数据？';
      }

      await ElMessageBox.confirm(
        confirmMessage,
        '风险操作确认',
        {
          confirmButtonText: '备份并继续',
          cancelButtonText: '直接执行',
          type: 'warning',
          distinguishCancelAndClose: true,
        }
      ).then(async () => {
        // 用户选择备份并继续
        emit('execution-state-change', true);
        await executeBackupQueryInBackground(selectSql, tableName, sql);
        // 如果备份失败但用户选择继续执行，executeBackupQueryInBackground 内部会处理
      }).catch((action) => {
        if (action === 'cancel') {
          // 用户选择直接执行
          proceedWithExecution(sql);
        } else {
          // 用户关闭了对话框
          resetExecutingState();
        }
      });
    } else {
      // 不是需要备份的操作，直接执行
      await proceedWithExecution(sql);
    }
  } catch (error) {
    // 出错时关闭加载提示
    loading.close();
    console.error('Error during backup preparation:', error);
    ElMessage.error('分析语句时出错，请检查语法或联系管理员。');
  }
};

const copyBackupSql = () => {
  navigator.clipboard.writeText(backupSqlForDialog.value).then(() => {
    ElMessage.success('SQL已复制到剪贴板');
  }).catch(err => {
    ElMessage.error('复制失败');
  });
};

// 从localStorage获取管理员定义的ClickHouse数据源列表
const getAdminDefinedClickhouseIds = () => {
  try {
    const savedIds = localStorage.getItem('admin_defined_clickhouse_ids');
    if (savedIds) {
      return JSON.parse(savedIds);
    }
  } catch (e) {
    console.error('解析管理员定义的ClickHouse数据源列表失败:', e);
  }
  return [];
};

// 管理员将数据源标记为ClickHouse
const markAsClickhouse = (datasourceId) => {
  if (!datasourceId) {
    ElMessage.error('无法标记：未选择数据源');
    return;
  }
  
  if (!props.isAdmin) {
    ElMessage.error('只有管理员可以执行此操作');
    return;
  }
  
  try {
    // 获取当前列表
    const currentIds = getAdminDefinedClickhouseIds();
    
    // 检查是否已存在
    if (currentIds.includes(datasourceId)) {
      ElMessage.info('此数据源已被标记为ClickHouse');
      return;
    }
    
    // 添加新ID
    currentIds.push(datasourceId);
    
    // 保存到localStorage
    localStorage.setItem('admin_defined_clickhouse_ids', JSON.stringify(currentIds));
    
    // 提示用户
    ElMessage.success(`数据源ID ${datasourceId} 已被标记为ClickHouse数据源`);
    
    // 刷新界面
    editModeEnabled.value = false;
    nextTick(() => {
      ElMessage.warning('已禁用编辑模式，因为ClickHouse数据源不支持修改操作');
    });
  } catch (e) {
    console.error('标记ClickHouse数据源失败:', e);
    ElMessage.error('操作失败: ' + e.message);
  }
};

// 管理员取消将数据源标记为ClickHouse
const unmarkAsClickhouse = (datasourceId) => {
  if (!datasourceId) {
    ElMessage.error('无法取消标记：未选择数据源');
    return;
  }
  
  if (!props.isAdmin) {
    ElMessage.error('只有管理员可以执行此操作');
    return;
  }
  
  try {
    // 获取当前列表
    const currentIds = getAdminDefinedClickhouseIds();
    
    // 检查是否存在
    if (!currentIds.includes(datasourceId)) {
      const datasource = props.databases.find(db => db.id === datasourceId);
      // 检查是否在硬编码列表中
      if (KNOWN_CLICKHOUSE_IDS.includes(datasourceId) || 
          (datasource && KNOWN_CLICKHOUSE_NAMES.includes(datasource.name))) {
        ElMessage.warning('此数据源在内置ClickHouse列表中，无法移除');
        return;
      }
      
      ElMessage.info('此数据源未被标记为ClickHouse');
      return;
    }
    
    // 移除ID
    const newIds = currentIds.filter(id => id !== datasourceId);
    
    // 保存到localStorage
    localStorage.setItem('admin_defined_clickhouse_ids', JSON.stringify(newIds));
    
    // 检查数据源是否是通过其他方式被识别为ClickHouse的
    const datasource = props.databases.find(db => db.id === datasourceId);
    if (datasource && (
        datasource.isClickhouse === true ||
        (datasource.engine && datasource.engine.toLowerCase().includes('clickhouse')) ||
        (datasource.driver && datasource.driver.toLowerCase().includes('clickhouse')) ||
        (datasource.url && datasource.url.toLowerCase().includes('clickhouse')) ||
        (datasource.host && datasource.host.toLowerCase().includes('clickhouse')) ||
        (datasource.name && datasource.name.toLowerCase().includes('clickhouse')) ||
        (datasource.type && datasource.type.toLowerCase().includes('clickhouse')) ||
        (KNOWN_CLICKHOUSE_IDS.includes(datasourceId)) ||
        (KNOWN_CLICKHOUSE_NAMES.includes(datasource.name))
      )) {
      ElMessage.warning('此数据源仍会被识别为ClickHouse，因为有其他识别条件匹配');
    } else {
      ElMessage.success(`数据源ID ${datasourceId} 已被移除出ClickHouse数据源列表`);
    }
  } catch (e) {
    console.error('取消标记ClickHouse数据源失败:', e);
    ElMessage.error('操作失败: ' + e.message);
  }
};

// 特殊数据源ID列表 - 这些ID强制识别为ClickHouse
const KNOWN_CLICKHOUSE_IDS = [
  4, // 从截图中看到的ID
  '4', // 字符串形式
];

// 特殊数据源名称列表 - 这些名称强制识别为ClickHouse
const KNOWN_CLICKHOUSE_NAMES = [
  '102', // 从截图中看到的名称
];

// 检查数据源是否为Clickhouse
const isClickhouseDataSource = () => {
  // 获取当前数据源ID
  const dsId = selectedDatasourceId.value;
  
  // 如果没有数据源ID，默认不是Clickhouse
  if (!dsId) return false;
  
  // 0. 先检查特殊ID列表 - 这会覆盖所有其他规则
  if (KNOWN_CLICKHOUSE_IDS.includes(dsId)) {
    console.log(`数据源ID ${dsId} 在已知ClickHouse ID列表中，强制识别为ClickHouse`);
    return true;
  }
  
  // 0.1 检查管理员定义的列表
  const adminDefinedIds = getAdminDefinedClickhouseIds();
  if (adminDefinedIds.includes(dsId)) {
    console.log(`数据源ID ${dsId} 在管理员定义的ClickHouse ID列表中，强制识别为ClickHouse`);
    return true;
  }
  
  // 通过ID判断数据源类型
  const datasource = props.databases.find(db => db.id === dsId);
  if (!datasource) return false;
  
  const dsName = datasource.name || '';
  const dsType = datasource.type || '';
  
  // 0.2 检查特殊名称列表
  if (KNOWN_CLICKHOUSE_NAMES.includes(dsName)) {
    console.log(`数据源名称 "${dsName}" 在已知ClickHouse名称列表中，强制识别为ClickHouse`);
    return true;
  }
  
  // 1. 检查是否有明确的Clickhouse标记
  // 注意：我们忽略isClickhouse: false，因为从截图来看这个标记可能不准确
  if (datasource.isClickhouse === true) {
    return true;
  }
  
  // 2. 检查数据源的引擎或者驱动类型
  if (datasource.engine && typeof datasource.engine === 'string') {
    if (datasource.engine.toLowerCase().includes('clickhouse')) {
      return true;
    }
  }
  
  if (datasource.driver && typeof datasource.driver === 'string') {
    if (datasource.driver.toLowerCase().includes('clickhouse')) {
      return true;
    }
  }
  
  // 3. 检查数据源连接URL或其他连接信息
  if (datasource.url && typeof datasource.url === 'string') {
    if (datasource.url.toLowerCase().includes('clickhouse')) {
      return true;
    }
  }
  
  if (datasource.host && typeof datasource.host === 'string') {
    if (datasource.host.toLowerCase().includes('clickhouse')) {
      return true;
    }
  }
  
  // 4. 其他通用检测逻辑
  const isClickhouse = 
    // 检查数据源名称是否包含clickhouse (不区分大小写)
    dsName.toLowerCase().includes('clickhouse') || 
    // 检查数据源类型是否包含clickhouse (不区分大小写)
    dsType.toLowerCase().includes('clickhouse') ||
    // 检查数据源ID是否包含clickhouse (不区分大小写)
    dsId.toString().toLowerCase().includes('clickhouse') ||
    // 检查数据源名称是否包含ch、ck等常见缩写 (不区分大小写)
    /\b(ch_|ck_|clickhouse)/i.test(dsName);
    
  // 5. 记录检测过程的详细信息，便于调试
  console.log('ClickHouse数据源检测结果:', { 
    dsId, 
    dsName, 
    dsType, 
    isClickhouse,
    // 记录所有检测项的结果
    isInKnownIdList: KNOWN_CLICKHOUSE_IDS.includes(dsId),
    isInKnownNameList: KNOWN_CLICKHOUSE_NAMES.includes(dsName),
    isInAdminDefinedList: adminDefinedIds.includes(dsId),
    hasDirectFlag: datasource.isClickhouse === true,
    engineIsClickhouse: datasource.engine?.toLowerCase().includes('clickhouse'),
    driverIsClickhouse: datasource.driver?.toLowerCase().includes('clickhouse'),
    urlContainsClickhouse: datasource.url?.toLowerCase().includes('clickhouse'),
    hostContainsClickhouse: datasource.host?.toLowerCase().includes('clickhouse'),
    nameContainsClickhouse: dsName.toLowerCase().includes('clickhouse'),
    typeContainsClickhouse: dsType.toLowerCase().includes('clickhouse'),
    idContainsClickhouse: dsId.toString().toLowerCase().includes('clickhouse'),
    matchesPattern: /\b(ch_|ck_|clickhouse)/i.test(dsName),
    // 输出完整的数据源对象，帮助调试
    datasource: JSON.stringify(datasource)
  });
  
  return isClickhouse;
};

const handleCellDblClick = (rowData, column, isReadOnly) => {
  console.log('单元格双击 - 原始参数:', { rowData, column, isReadOnly });
  
  // 如果是只读，但当前有编辑权限且允许编辑，则自动切换到编辑模式并重试
  if (isReadOnly && canEnableEditMode.value && currentEditModeEnabled.value) {
    console.log('检测到只读状态，自动切换到编辑模式并重试');
    handleEditModeChange(true);
    setTimeout(() => {
      handleCellDblClick(rowData, column, false);
    }, 100);
    return;
  }
  
  // 兼容不同格式的参数
  let row, columnKey, originalValue;
  
  // 重置编辑上下文，防止残留状态
  editingCellContext.value = null;
  
  // 处理来自EditableResultTable的参数格式
  if (rowData && typeof column === 'string') {
    row = rowData;
    columnKey = column;
    originalValue = row[columnKey];
    console.log('EditableResultTable格式参数:', { row, columnKey, originalValue });
  }
  // 处理来自 el-table 的参数格式
  else if (rowData && column && column.property) {
    row = rowData;
    columnKey = column.property;
    originalValue = row[columnKey];
    console.log('el-table格式参数:', { row, columnKey, originalValue });
  }
  // 处理对象形式的参数 { row, column, value }
  else if (rowData && rowData.row && (rowData.column || rowData.columnKey)) {
    row = rowData.row;
    columnKey = rowData.column || rowData.columnKey;
    originalValue = rowData.value !== undefined ? rowData.value : row[columnKey];
    console.log('对象格式参数:', { row, columnKey, originalValue });
  }
  else {
    console.error('无法识别的单元格双击参数格式:', { rowData, column, isReadOnly });
    return;
  }
  
  console.log('处理后的单元格参数:', { row, columnKey, originalValue });
  
  // 检查是否为Clickhouse数据源
  const clickhouseDataSource = isClickhouseDataSource();
  
  // 如果是只读模式或ClickHouse数据源，只显示内容不允许编辑
  if (isReadOnly || clickhouseDataSource) {
    cellDetailTitle.value = `查看 ${columnKey}`;
    cellDetailContent.value = originalValue;
    isEditing.value = false; // 确保不处于编辑模式
    cellDetailVisible.value = true;
    return;
  } 
  else {
    // 编辑模式，直接进入编辑状态
    cellDetailTitle.value = `编辑 ${columnKey}`;
    cellDetailContent.value = originalValue;
    cellEditValue.value = originalValue;
    
    // 在单字段编辑时不显示备份语句
    backupSql.value = ''; // 清空备份语句
    
    // 保存编辑上下文，这对于后续编辑操作很重要
    editingCellContext.value = { row, column: columnKey, value: originalValue };
    console.log('已设置编辑上下文:', editingCellContext.value);
    
    // 保存当前编辑的行到tab对象中
    emit('update:tab', {
      ...props.tab,
      currentEditingRow: row
    });
    
    // 强制设置为编辑模式
    isEditing.value = true;
    
    // 创建一个标记，表示这是通过双击进入的编辑模式
    window._isDoubleClickEdit = true;
    
    // 显示对话框
    cellDetailVisible.value = true;
    
    // 使用setTimeout确保DOM已经更新
    setTimeout(() => {
      console.log('检查编辑状态:', isEditing.value);
      if (!isEditing.value) {
        console.log('编辑状态被重置，强制恢复');
        isEditing.value = true;
      }
      
      // 尝试聚焦到输入框
      const inputRef = document.querySelector('.cell-edit-content .el-input__inner');
      if (inputRef) {
        console.log('找到输入框元素，尝试聚焦');
        inputRef.focus();
      } else {
        console.log('未找到输入框元素');
      }
    }, 100);
  }
};

const executeAfterBackup = async () => {
  backupDialogVisible.value = false;
  // 添加短暂延迟，让备份弹窗完全关闭后再显示执行状态
  await nextTick();
  setTimeout(() => {
    proceedWithExecution(originalSqlToExecute.value);
  }, 200); // 200ms延迟，确保用户体验流畅
};

// 处理格式化 SQL
const handleFormatSql = () => {
  emit('format-sql', {
    tabId: props.tab.id,
    sql: sqlContent.value
  })
}

// 处理保存查询
const handleSaveQuery = () => {
  if (!selectedDatasourceId.value) {
    ElMessage.warning('请先选择数据源')
    return
  }

  const sql = sqlContent.value.trim()
  if (!sql) {
    ElMessage.warning('请输入 SQL 查询语句')
    return
  }

  emit('save-query', {
    sql: sql,
    datasourceId: selectedDatasourceId.value,
    schema: selectedSchema.value
  })
}

// 添加一个防止重复处理的标志
let isProcessingDblClick = false;



// 添加格式化完整值的函数
const formatCompleteValue = (value) => {
  if (value === null || value === undefined) return '';
  
  // 处理时间格式
  if (typeof value === 'string' && 
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
    return value.replace('T', ' ');
  }
  
  return String(value);
}

// 添加复制内容到剪贴板的方法
const copyCellContent = async () => {
  try {
    await navigator.clipboard.writeText(cellDetailContent.value);
    ElMessage.success('内容已复制到剪贴板');
  } catch (err) {
    ElMessage.error('复制失败: ' + err.message);
  }
}

// 添加字段名截断函数
const truncateLabel = (label) => {
  if (!label) return '';
  return label.length > 30 ? label.substring(0, 10) + '...' : label;
}

// 添加数据值截断函数
const truncateValue = (value) => {
  if (value === null || value === undefined) return '';
  
  // 先处理日期格式
  let formattedValue = value;
  if (typeof value === 'string' && 
      /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(value)) {
    formattedValue = value.replace('T', ' ');
  }
  
  // 然后截断长度
  if (typeof formattedValue === 'string' && formattedValue.length > 30) {
    return formattedValue.substring(0, 30) + '...';
  }
  
  return formattedValue;
}

// 添加客户端分页相关的方法
const handleSizeChange = (newSize) => {
  if (isExecuting.value) return; // 防止在加载时改变分页
  pageSize.value = newSize;
  
  // 页大小变化时，可能需要调整当前页，确保不会出现空页
  const maxPage = Math.ceil((props.tab.results?.length || 0) / pageSize.value);
  if (currentPage.value > maxPage) {
    currentPage.value = Math.max(1, maxPage);
  }
  
  console.log(`分页大小变更为 ${newSize}，当前页: ${currentPage.value}`);
};

const handleCurrentChange = (newPage) => {
  if (isExecuting.value) return; // 防止在加载时改变分页
  currentPage.value = newPage;
  console.log(`页码变更为 ${newPage}`);
};

// 当接收新的SQL或改变数据源/schema时，重置分页到第一页
watch(() => [sqlContent.value, selectedDatasourceId.value, selectedSchema.value], () => {
  // 只有当用户改变SQL内容或切换数据源/schema时重置分页
  // 避免因为结果更新而无限循环
  currentPage.value = 1;
});

// 监控tab的执行状态，更新执行日志
watch(() => props.tab.executed, (isExecuted, oldValue) => {
  if (isExecuted && !oldValue && !isExecuting.value) {
    // 新执行完成的查询，但不是当前执行中的（可能是从其他地方触发的）
    const sql = props.tab.sql || sqlContent.value;
    addExecutionLog(`执行SQL: ${sql.trim()}`, props.tab.error ? 'error' : 'success');
  }
});

// 监控执行状态和结果，记录到日志
watch(() => isExecuting.value, (val, oldVal) => {
  // 当处理多条SQL语句时，由循环内部处理日志，这里直接返回
  if (isMultiStatement.value) {
    return;
  }
  
  if (oldVal && !val) { // 执行结束
    const execTime = Date.now() - queryStartTime;
    const status = props.tab.error ? 'error' : 'success';
    let message = '';
    
    if (props.tab.error) {
      message = `执行失败: ${props.tab.error}`;
      // 错误时不再自动切换标签页
    } else if (isDML(sqlContent.value)) {
      message = `在 ${execTime} ms 内完成`;
      if (props.tab.messages) {
        message += `，${props.tab.messages}`;
      } else if (props.tab.affected_rows !== null && props.tab.affected_rows !== undefined) {
        message += `，${props.tab.affected_rows} 行受影响`;
      } else {
        message += '，操作成功';
      }
    } else if (isDDL(sqlContent.value)) {
      message = `在 ${execTime} ms 内完成`;
      message += `，${props.tab.messages || '操作成功'}`;
      // 不再自动切换标签页
    } else if (props.tab.results && props.tab.results.length > 0) {
      message = `在 ${execTime} ms 内完成，返回 ${props.tab.results.length} 行数据`;
      // 不再自动切换标签页
    } else {
      message = `在 ${execTime} ms 内完成，未返回数据`;
      // 不再自动切换标签页
    }
    
    // 确保标签页有executionLogs属性
    if (!props.tab.executionLogs) {
      props.tab.executionLogs = [];
    }
    
    // 更新之前的pending消息为最终结果
    const pendingLogIndex = props.tab.executionLogs.findIndex(log => log.status === 'pending');
    if (pendingLogIndex >= 0) {
      // 更新状态和结果
      props.tab.executionLogs[pendingLogIndex].status = status;
      props.tab.executionLogs[pendingLogIndex].result = message;
      
      // 通知父组件更新
      emit('update:tab', {
        ...props.tab,
        executionLogs: [...props.tab.executionLogs]
      });
    } else {
      // 如果找不到pending的日志（不应该发生），就直接添加新日志
      addExecutionLog(message, status);
    }
  }
});

const startResize = (event) => {
  event.preventDefault();
  const startY = event.clientY || (event.touches && event.touches[0].clientY);
  const startHeight = editorHeight.value;
  const resizer = event.target;
  resizer.classList.add('active');

  const resize = (moveEvent) => {
    const currentY = moveEvent.clientY || (moveEvent.touches && moveEvent.touches[0].clientY);
    const deltaY = currentY - startY;
    const newHeight = Math.max(100, Math.min(window.innerHeight * 0.8, startHeight + deltaY));
    editorHeight.value = newHeight;
  };

  const stopResize = () => {
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopResize);
    document.removeEventListener('touchmove', resize);
    document.removeEventListener('touchend', stopResize);
    resizer.classList.remove('active');
    
    // 保存编辑器高度到本地存储
    localStorage.setItem('sql_editor_height', editorHeight.value.toString());
  };

  document.addEventListener('mousemove', resize);
  document.addEventListener('mouseup', stopResize);
  document.addEventListener('touchmove', resize, { passive: false });
  document.addEventListener('touchend', stopResize);
};

// 根据窗口大小调整编辑器初始高度
const adjustEditorHeight = () => {
  const windowHeight = window.innerHeight
  if (editorHeight.value > windowHeight * 0.8) {
    editorHeight.value = windowHeight * 0.5 // 默认为窗口高度的一半
  }
}
// 在组件挂载时初始化编辑器
  // 处理查询结果事件
const handleQueryResult = (event) => {
  const result = event.detail;
  
  console.log('====== handleQueryResult 开始 ======');
  console.log('result.splitSqlStatements:', result.splitSqlStatements);
  console.log('props.tab.splitSqlStatements:', props.tab.splitSqlStatements);
  console.log('result.all_results 长度:', result.all_results ? result.all_results.length : 'undefined');
  console.log('result.sqls:', result.sqls);
  console.log('result.sql:', result.sql);

  // 如果禁用UI更新，直接返回不处理
  if (result.disableUIUpdate) {
    console.log('收到禁用UI更新的查询结果，跳过处理');
    return;
  }

  // 确保是当前标签页的结果
  if (result.tabId === props.tab.id) {
    console.log('====== 开始处理查询结果 ======');
    console.log('当前标签页ID:', props.tab.id);
    console.log('完整的结果对象:', result);

    // 确保resultTabs数组存在
    if (!props.tab.resultTabs) {
      console.log('初始化resultTabs数组');
      props.tab.resultTabs = [];
    }

    // 清空之前的批量变更结果，因为有新的查询结果
    if (props.tab.multiDmlResults) {
      console.log('清空之前的批量变更结果');
      props.tab.multiDmlResults = null;
      props.tab.messages = null;
    }

    // 简化逻辑：无论什么SQL执行都创建result标签页
    console.log('====== 创建结果标签页 ======');
    console.log('result对象:', result);

    // 检查是否达到标签页上限（10个）
    if (props.tab.resultTabs.length >= 10) {
      console.log('已达到标签页上限(10)，清空所有标签页并从result 1重新开始');
      props.tab.resultTabs = [];
    }

    // 检查是否有多个结果集
    if (result.all_results && Array.isArray(result.all_results) && result.all_results.length > 0) {
      console.log('====== 处理多结果集 ======');
      console.log('结果集数量:', result.all_results.length);

      // 为每个结果集创建单独的标签页
      for (let i = 0; i < result.all_results.length; i++) {
        const resultSet = result.all_results[i];
        console.log(`处理第 ${i + 1} 个结果集:`, resultSet);

        // 获取对应的单独SQL语句 - 改进的SQL获取逻辑
        let currentSql = '未知SQL';
        
        // 首先尝试从result.splitSqlStatements获取
        if (result.splitSqlStatements && Array.isArray(result.splitSqlStatements) && result.splitSqlStatements[i]) {
          currentSql = result.splitSqlStatements[i];
          console.log(`[修复] 使用result.splitSqlStatements[${i}]:`, currentSql);
        } 
        // 然后尝试从props.tab.splitSqlStatements获取
        else if (props.tab.splitSqlStatements && Array.isArray(props.tab.splitSqlStatements) && props.tab.splitSqlStatements[i]) {
          currentSql = props.tab.splitSqlStatements[i];
          console.log(`[修复] 使用props.tab.splitSqlStatements[${i}]:`, currentSql);
        } 
        // 尝试从result.sqls获取
        else if (result.sqls && Array.isArray(result.sqls) && i < result.sqls.length) {
          currentSql = result.sqls[i];
          console.log(`[修复] 使用result.sqls[${i}]:`, currentSql);
        } 
        // 尝试从原始SQL分割获取
        else if (props.tab.sql) {
          // 重新分割原始SQL
          const originalSql = props.tab.sql;
          const sqlStatements = [];
          let currentStatement = '';
          let inSingleQuote = false;
          let inDoubleQuote = false;
          let inBacktick = false;
          let escaped = false;

          for (let j = 0; j < originalSql.length; j++) {
            const char = originalSql[j];
            if (escaped) {
              currentStatement += char;
              escaped = false;
              continue;
            }
            if (char === '\\') {
              currentStatement += char;
              escaped = true;
              continue;
            }
            if (char === "'" && !inDoubleQuote && !inBacktick) {
              currentStatement += char;
              inSingleQuote = !inSingleQuote;
              continue;
            }
            if (char === '"' && !inSingleQuote && !inBacktick) {
              currentStatement += char;
              inDoubleQuote = !inDoubleQuote;
              continue;
            }
            if (char === '`' && !inSingleQuote && !inDoubleQuote) {
              currentStatement += char;
              inBacktick = !inBacktick;
              continue;
            }
            if (char === ';' && !inSingleQuote && !inDoubleQuote && !inBacktick) {
              if (currentStatement.trim()) {
                sqlStatements.push(currentStatement.trim());
              }
              currentStatement = '';
              continue;
            }
            currentStatement += char;
          }
          if (currentStatement.trim()) {
            sqlStatements.push(currentStatement.trim());
          }
          
          const filteredStatements = sqlStatements.filter(sql => sql.trim().length > 0);
          if (filteredStatements[i]) {
            currentSql = filteredStatements[i];
            console.log(`[修复] 重新分割原始SQL获取第${i+1}条:`, currentSql);
          } else {
            console.error(`[错误] 第${i+1}个结果集无法获取SQL语句，重新分割后仍然无效`);
            console.error('重新分割的SQL语句数量:', filteredStatements.length);
            console.error('请求的索引:', i);
            console.error('result.splitSqlStatements:', result.splitSqlStatements);
            console.error('props.tab.splitSqlStatements:', props.tab.splitSqlStatements);
            console.error('result.sqls:', result.sqls);
            console.error('result.sql:', result.sql);
            console.error('props.tab.sql:', props.tab.sql);
            currentSql = `[错误] 无法获取第${i+1}条SQL语句`;
          }
        } 
        // 最后尝试从result.sql获取
        else if (result.sql && !result.sql.includes(';')) {
          currentSql = result.sql;
          console.log(`[修复] 使用result.sql:`, currentSql);
        } 
        else {
          console.error(`[错误] 第${i+1}个结果集无法获取SQL语句，所有来源都为空或无效`);
          console.error('result.splitSqlStatements:', result.splitSqlStatements);
          console.error('props.tab.splitSqlStatements:', props.tab.splitSqlStatements);
          console.error('result.sqls:', result.sqls);
          console.error('result.sql:', result.sql);
          console.error('props.tab.sql:', props.tab.sql);
          currentSql = `[错误] 无法获取第${i+1}条SQL语句`;
        }

        // 获取SQL文本用于标签页显示
        const sqlLabel = currentSql.length > 50 ? currentSql.substring(0, 50) + '...' : currentSql;

        // 判断是否为DML语句（只有真正的数据修改语句才显示受影响行数）
        const isDmlStatement = isDML(currentSql);

        // 判断是否为SELECT查询语句
        const isSelectStatement = isSelect(currentSql);

        // 检查当前结果集是否有错误
        const hasError = resultSet.has_error || resultSet.error;

        // 创建新的结果标签页
        const newResultTab = {
          id: `result-tab-${Date.now()}-${i}`,
          sql: currentSql,  // 使用完整的单独SQL语句
          // 如果有错误，不显示错误信息作为表格数据，而是显示在错误提示中
          results: hasError ? [] : (resultSet.rows || []),
          columns: hasError ? [] : (resultSet.columns || []),
          timestamp: Date.now(),
          currentPage: 1,
          pageSize: 20,
          executed: true,
          error: hasError ? (resultSet.error || '执行失败') : null,
          messages: hasError ? null : (resultSet.message || (isDmlStatement ? `成功执行，${resultSet.affected_rows || 0} 行受影响` : '')),
          rowCount: hasError ? 0 : (resultSet.affected_rows !== undefined ? resultSet.affected_rows : (resultSet.rows ? resultSet.rows.length : 0)),
          affected_rows: hasError ? 0 : resultSet.affected_rows,
          isDmlResult: isDmlStatement,
          isSelect: isSelectStatement  // 添加isSelect属性用于判断是否显示编辑按钮
        };

        console.log(`新创建的结果标签页 ${i + 1}:`, newResultTab);

        // 添加到结果标签页数组
        props.tab.resultTabs.push(newResultTab);

        // 初始化新标签页的编辑模式状态
        const newTabIndex = props.tab.resultTabs.length - 1;
        resultTabsEditMode.value[newTabIndex] = false;
        console.log(`初始化新标签页 ${newResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);
      }

      // 设置主结果标签页显示第一个结果集的数据
      if (props.tab.resultTabs.length > 0) {
        const firstResultTab = props.tab.resultTabs[0];
        props.tab.results = firstResultTab.results || [];
        props.tab.columns = firstResultTab.columns || [];
        props.tab.error = firstResultTab.error || null;
        props.tab.messages = firstResultTab.messages || null;
        props.tab.executed = true;
        
        // 切换到主结果标签页
        activeResultTab.value = 'result';
        console.log('切换到主结果标签页:', activeResultTab.value);
      }
    } else {
      // 单个结果集处理
      console.log('====== 处理单个结果集 ======');

      // 获取SQL文本用于标签页显示
      const sqlText = result.sql || props.tab.sql || '[错误] 无法获取SQL语句';
      console.log('[单个结果集] sqlText:', sqlText);
      console.log('[单个结果集] result.sql:', result.sql);
      console.log('[单个结果集] props.tab.sql:', props.tab.sql);
      const sqlLabel = sqlText.length > 50 ? sqlText.substring(0, 50) + '...' : sqlText;

      // 判断是否为DML语句（UPDATE, INSERT, DELETE）
      const isDmlStatement = isDML(result.sql || props.tab.sql || '');

      // 检查是否有错误
      const hasError = result.error;

      // 创建新的结果标签页
      const newResultTab = {
        id: `result-tab-${Date.now()}`,
        sql: sqlLabel,
        // 如果有错误，不显示错误信息作为表格数据，而是显示在错误提示中
        results: hasError ? [] : (result.results || []),
        columns: hasError ? [] : (result.columns || []),
        timestamp: Date.now(),
        currentPage: 1,
        pageSize: 20,
        executed: true,
        error: result.error || null, // 正确传递错误信息
        messages: result.error ? null : (result.messages || (isDmlStatement ? `成功执行，${result.affected_rows || 0} 行受影响` : '')),
        rowCount: hasError ? 0 : (result.affected_rows !== undefined ? result.affected_rows : (result.results ? result.results.length : 0)),
        affected_rows: hasError ? 0 : result.affected_rows,
        isDmlResult: isDmlStatement // 标记是否为DML结果
      };

      console.log('新创建的结果标签页:', newResultTab);

      // 同时更新主结果标签页和额外结果标签页
      if (hasError) {
        // 如果有错误，主结果标签页显示错误信息
        props.tab.results = [];
        props.tab.columns = [];
        props.tab.error = result.error || '执行失败';
        props.tab.messages = null;
        props.tab.executed = true;
      } else {
        // 如果没有错误，主结果标签页显示数据
        props.tab.results = result.results || [];
        props.tab.columns = result.columns || [];
        props.tab.error = null;
        props.tab.messages = result.messages || (isDmlStatement ? `成功执行，${result.affected_rows || 0} 行受影响` : '');
        props.tab.executed = true;
      }

      // 添加到结果标签页数组
      props.tab.resultTabs.push(newResultTab);

      // 初始化新标签页的编辑模式状态
      const newTabIndex = props.tab.resultTabs.length - 1;
      resultTabsEditMode.value[newTabIndex] = false;
      console.log(`初始化新标签页 ${newResultTab.id} (索引 ${newTabIndex}) 的编辑模式状态为: false (只读模式)`);

      // 切换到主结果标签页
      activeResultTab.value = 'result';
      console.log('切换到主结果标签页:', activeResultTab.value);
    }

    // 强制触发响应式更新
    nextTick(() => {
      console.log('DOM更新后，当前活动标签页:', activeResultTab.value);
      console.log('当前所有标签页:', props.tab.resultTabs.map(tab => tab.id));
    });

    // 更新父组件中的tab对象
    emit('update:tab', {...props.tab});
    console.log('已更新父组件tab对象');
  }

  // 重置执行状态
  resetExecutingState();
};


// 处理切换结果标签页事件
const handleSwitchToResultTab = (event) => {
  const { tabId, resultTabId } = event.detail;
  console.log('ActiveQueryPanel收到切换结果标签页事件:', { tabId, resultTabId });

  if (tabId !== props.tab.id) {
    console.log('切换事件不属于当前标签页，忽略');
    return;
  }

  // 切换到指定的结果标签页
  activeResultTab.value = resultTabId;
  console.log(`已切换到结果标签页: ${resultTabId}`);
};

// 处理自动退出编辑模式事件
const handleAutoExitEditMode = (event) => {
  console.log(`[handleAutoExitEditMode] 收到事件:`, event);

  const { tabId, saveChanges, reason } = event.detail;

  console.log(`[handleAutoExitEditMode] 接收到自动退出编辑模式事件:`, {
    tabId,
    saveChanges,
    reason,
    currentTabId: props.tab.id,
    currentEditMode: currentEditModeEnabled.value,
    activeResultTab: activeResultTab.value
  });

  // 检查事件是否属于当前标签页
  if (tabId !== props.tab.id) {
    console.log('自动退出编辑模式事件不属于当前标签页，忽略');
    return;
  }

  // 退出编辑模式
  console.log(`[handleAutoExitEditMode] 正在退出编辑模式，保存修改: ${saveChanges}`);
  console.log(`[handleAutoExitEditMode] 当前编辑模式状态: ${currentEditModeEnabled.value}`);

  if (saveChanges) {
    // 成功时保存修改，不需要额外操作，因为修改已经提交到服务器
    console.log('[handleAutoExitEditMode] 提交成功，保持当前修改状态');
  } else {
    // 失败时不保存修改，需要恢复到原始状态
    console.log('[handleAutoExitEditMode] 提交失败，恢复到原始状态');
    // 这里可以添加恢复原始数据的逻辑，但通常表格组件会自己处理
  }

  // 如果当前在操作结果标签页（使用时间戳作为ID），先切换回主结果标签页
  if (activeResultTab.value.startsWith('result-tab-')) {
    const tabId = activeResultTab.value.replace('result-tab-', '');
    if (tabId.length > 10) { // 时间戳长度通常大于10位，说明这是操作结果标签页
      console.log('[handleAutoExitEditMode] 当前在操作结果标签页，切换回主结果标签页');
      activeResultTab.value = 'result';
    }
  }

  // 关闭编辑模式
  console.log('[handleAutoExitEditMode] 调用 handleEditModeChange(false)');
  console.log('[handleAutoExitEditMode] 当前活动标签页:', activeResultTab.value);
  console.log('[handleAutoExitEditMode] 当前编辑模式状态:', currentEditModeEnabled.value);
  handleEditModeChange(false);

  console.log(`[handleAutoExitEditMode] 已退出编辑模式，原因: ${reason}`);
  console.log(`[handleAutoExitEditMode] 退出后编辑模式状态: ${currentEditModeEnabled.value}`);
};

onMounted(async () => {
  // 初始化CodeMirror
  await nextTick()
  initCodeMirror()
  
  // 延迟一点时间，确保编辑器已经初始化
  setTimeout(() => {
    // 通知编辑器已准备就绪，并传递编辑器实例
    emit('editor-ready', {
      tabId: props.tab.id,
      editor: editorView
    });
    console.log('[ActiveQueryPanel] 已发送editor-ready事件，传递了编辑器实例');
  }, 100);
  
  // 从本地存储加载编辑器高度
  const savedHeight = localStorage.getItem('sql_editor_height')
  if (savedHeight) {
    // 确保高度不超过窗口的80%
    const maxHeight = window.innerHeight * 0.8
    editorHeight.value = Math.min(parseInt(savedHeight) || 300, maxHeight)
  }
  
  adjustEditorHeight()
  window.addEventListener('resize', adjustEditorHeight)
  
  // 监听查询结果事件
  document.addEventListener('query-result', handleQueryResult)

  // 监听切换结果标签页事件
  document.addEventListener('switch-to-result-tab', handleSwitchToResultTab)

  // 监听自动退出编辑模式事件
  document.addEventListener('auto-exit-edit-mode', handleAutoExitEditMode)
  console.log('[ActiveQueryPanel] 已添加自动退出编辑模式事件监听器，当前标签页ID:', props.tab.id)
  
  // 如果组件挂载时已经有数据源和schema，主动通知父组件
  console.log('组件挂载时状态:', { 
    datasourceId: selectedDatasourceId.value, 
    schema: selectedSchema.value,
    tabId: props.tab.id
  })
  
  if (selectedDatasourceId.value) {
    // 延迟一下，确保父组件已准备好接收事件
    setTimeout(() => {
      console.log('组件挂载后主动通知父组件当前数据源和schema状态')
      emit('tab-database-change', {
        tabId: props.tab.id,
        datasourceId: selectedDatasourceId.value,
        schema: selectedSchema.value
      })
      
      // 直接更新父组件中的 tab 对象
      emit('update:tab', {
        ...props.tab,
        datasourceId: selectedDatasourceId.value,
        schema: selectedSchema.value
      })
    }, 100)
  }
  
  // 组件挂载时初始化编辑模式状态
  // 确保主结果标签页的编辑模式状态被正确初始化
  if (resultTabsEditMode.value['main'] === undefined) {
    // 始终默认为只读模式，用户需要手动点击"开启编辑"按钮
    resultTabsEditMode.value['main'] = false;
    console.log('组件挂载时初始化主结果标签的编辑模式状态为: false (只读模式)');

    // 确保父组件的编辑模式状态也是关闭的
    emit('update:tab', {
      ...props.tab,
      editModeEnabled: false
    });
  }
})

// 组件销毁时清理资源
onUnmounted(() => {
  console.log('[ActiveQueryPanel] 组件销毁，清理所有资源');
  
  // 立即设置销毁标志，阻止任何后续操作
  isComponentDestroyed = true;

  
  
  // 清理安全超时定时器
  if (safetyTimeoutId) {
    clearTimeout(safetyTimeoutId);
    safetyTimeoutId = null;
  }
  
  try {
    // 0. 清理窗口事件监听器 - 只清理本组件添加的监听器
    console.log('[ActiveQueryPanel] 移除窗口事件监听器');
    window.removeEventListener('resize', adjustEditorHeight);
    
    // 移除查询结果事件监听器
    document.removeEventListener('query-result', handleQueryResult);

    // 移除切换结果标签页事件监听器
    document.removeEventListener('switch-to-result-tab', handleSwitchToResultTab);

    // 移除自动退出编辑模式事件监听器
    document.removeEventListener('auto-exit-edit-mode', handleAutoExitEditMode);
    
    // 1. 安全移除编辑器事件监听器
    if (editorView && editorView.dom) {
      console.log('[ActiveQueryPanel] 移除编辑器DOM事件监听器');
      
      try {
        // 使用更安全的方式移除事件监听器
        const safeRemove = (element, event, handler) => {
          try {
            if (element && typeof element.removeEventListener === 'function' && typeof handler === 'function') {
              element.removeEventListener(event, handler);
              console.log(`[ActiveQueryPanel] 已移除 ${event} 事件监听器`);
            }
          } catch (e) {
            console.warn(`[ActiveQueryPanel] 移除 ${event} 事件监听器失败:`, e);
          }
        };
        
        // 只移除与编辑器相关的事件监听器
        if (editorView._dotKeyHandler) {
          safeRemove(editorView.dom, 'keydown', editorView._dotKeyHandler);
        }
        
        if (editorView._inputHandler) {
          safeRemove(editorView.dom, 'input', editorView._inputHandler);
        }
        
        // 不再移除全局事件监听器
        // safeRemove(editorView.dom, 'focus', null);
        // safeRemove(editorView.dom, 'blur', null);
        // safeRemove(editorView.dom, 'keyup', null);
      } catch (eventError) {
        console.warn('[ActiveQueryPanel] 移除事件监听器失败:', eventError.message);
      }
    }
    
    // 2. 从DOM树移除编辑器元素
    if (editorView && editorView.dom) {
      try {
        const editorDOM = editorView.dom;
        const editorParent = editorDOM.parentNode;
        
        // 先清空内容
        editorDOM.innerHTML = '';
        
        // 然后从父元素中移除
        if (editorParent) {
          editorParent.removeChild(editorDOM);
          console.log('[ActiveQueryPanel] 已从DOM树移除编辑器元素');
        }
      } catch (domError) {
        console.warn('[ActiveQueryPanel] 移除DOM元素失败:', domError.message);
      }
    }
    
    // 3. 销毁编辑器实例
    if (editorView) {
      try {
        // 保存临时引用
        const tempView = editorView;
        
        // 先置空引用
        editorView = null;
        
        // 禁用关键方法，防止销毁过程中触发事件
        const noop = () => {};
        tempView.dispatch = noop;
        tempView.update = noop;
        
        // 然后调用销毁方法
        tempView.destroy();
        console.log('[ActiveQueryPanel] 编辑器实例已销毁');
      } catch (destroyError) {
        console.warn('[ActiveQueryPanel] 编辑器销毁失败:', destroyError.message);
      }
    }
    
    // 4. 清理组件引用
    cmEditor.value = null;
    
    // 5. 不再清理或修改全局状态
    console.log('[ActiveQueryPanel] 资源清理完成');
  } catch (error) {
    console.error('[ActiveQueryPanel] 清理资源时出错:', error);
  } finally {
    // 确保编辑器和DOM引用被清空
    editorView = null;
    cmEditor.value = null;
  }
});

defineExpose({
  selectedDatasourceId,
  selectedSchema,
  updateTableData: (newData) => {
    if (editableResultTableRef.value && editableResultTableRef.value.updateTableData) {
      editableResultTableRef.value.updateTableData(newData);
    }
    
    resultTableRefs.value.forEach((tableRef, index) => {
      if (tableRef && tableRef.updateTableData && resultTabs.value[index] && resultTabs.value[index].results) {
        // 使用分页数据而不是全部数据
        const paginatedData = getResultTabPaginatedData(resultTabs.value[index]);
        tableRef.updateTableData(paginatedData);
      }
    });
  },
  checkAndBackupBeforeExecute: (sql, tableName) => {
    // 直接调用 prepareBackupAndConfirm，如果有返回Promise则返回
    if (typeof prepareBackupAndConfirm === 'function') {
      return prepareBackupAndConfirm(sql, tableName);
    }
    // 兜底：直接返回已完成Promise
    return Promise.resolve();
  }
})

// 强制刷新自动补全缓存
async function forceRefreshAutocompleteCache() {
  console.log('[forceRefreshAutocompleteCache] 开始强制刷新自动补全缓存');
  
  const dsId = selectedDatasourceId.value;
  if (!dsId) {
    console.warn('[forceRefreshAutocompleteCache] 没有选择数据源，无法刷新缓存');
    return false;
  }
  
  try {
    // 从服务器获取最新的元数据
    const response = await getDatabaseFullMetadata(dsId);
    
    if (!response || typeof response !== 'object') {
      console.error('[forceRefreshAutocompleteCache] 获取到的元数据无效:', response);
      return false;
    }
    
    // 提取数据，兼容不同的响应格式
    const metadata = response.data || response.metadata || response;
    
    // 保存到本地存储
    const cacheKey = `autocomplete_cache_${dsId}`;
    const cacheData = {
      timestamp: Date.now(),
      data: metadata
    };
    
    try {
      localStorage.setItem(cacheKey, JSON.stringify(cacheData));
      console.log('[forceRefreshAutocompleteCache] 缓存已更新');
      
      // 处理新的缓存数据
      processAutocompleteCache(metadata);
      
      return true;
    } catch (storageError) {
      console.error('[forceRefreshAutocompleteCache] 保存缓存时出错:', storageError);
      return false;
    }
  } catch (error) {
    console.error('[forceRefreshAutocompleteCache] 刷新缓存失败:', error);
    return false;
  }
}

// 计算属性：判断当前用户是否可以启用编辑模式
const canEnableEditMode = computed(() => {
  const result = props.isAdmin || (props.userPermissions && props.userPermissions.canEdit);
  console.log('[DEBUG] canEnableEditMode 计算:', {
    isAdmin: props.isAdmin,
    userPermissions: props.userPermissions,
    canEdit: props.userPermissions?.canEdit,
    result: result
  });
  return result;
})

// 监视结果标签页编辑模式状态变化
watch(() => resultTabsEditMode.value, (newValues) => {
  console.log(`结果标签页编辑模式状态变更:`, newValues);
  
  // 设置标志位，防止在编辑模式状态变更时错误地切换标签页
  isUpdatingEditMode.value = true;
  
  // 确保在DOM更新后执行
  nextTick(() => {
    // 如果当前是主结果标签页
    if (activeResultTab.value === 'result') {
      const tableRef = getCurrentTableRef();
      if (tableRef) {
        console.log('主结果表isReadOnly更新为:', !newValues['main']);
        tableRef.isReadOnly = !newValues['main'];
      }
    }
    
    // 如果当前是额外结果标签页
    if (activeResultTab.value.startsWith('result-')) {
      let index;
      if (activeResultTab.value.startsWith('result-tab-')) {
        // 提取 'result-tab-X' 中的 X
        index = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
      } else {
        // 提取 'result-X' 中的 X
        index = parseInt(activeResultTab.value.replace('result-', '')) - 1;
      }
      const tableRef = resultTableRefs.value[index];
      if (tableRef) {
        console.log(`额外结果标签 ${activeResultTab.value} (索引 ${index}) isReadOnly更新为:`, !newValues[index]);
        tableRef.isReadOnly = !newValues[index];
      }
    }
    
    // 重置标志位
    setTimeout(() => {
      isUpdatingEditMode.value = false;
      console.log('编辑模式状态变更完成，重置标志位');
    }, 100);
  });
}, { deep: true });

// 监听活动结果标签页变化，更新开关状态
watch(activeResultTab, (newTab) => {
  console.log(`活动结果标签页变更为: ${newTab}`);

  // 如果是主结果标签页，确保它有初始化的编辑模式状态
  if (newTab === 'result') {
    if (resultTabsEditMode.value['main'] === undefined) {
      // 始终默认为只读模式，用户需要手动点击"开启编辑"按钮
      resultTabsEditMode.value['main'] = false;
      console.log('初始化主结果标签的编辑模式状态为: false (只读模式)');
    }
  }
  // 如果是额外结果标签页，确保它有初始化的编辑模式状态
  else if (newTab.startsWith('result-tab-') || newTab.startsWith('result-')) {
    // 通过ID在数组中查找实际索引
    const resultTabsArray = props.tab.resultTabs || [];
    const index = resultTabsArray.findIndex(tab => tab.id === newTab);

    if (index !== -1 && resultTabsEditMode.value[index] === undefined) {
      // 始终默认为只读模式，用户需要手动点击"开启编辑"按钮
      resultTabsEditMode.value[index] = false;
      console.log(`初始化结果标签 ${newTab} (索引 ${index}) 的编辑模式状态为: false (只读模式)`);
    }
  }
});

// 处理编辑模式变更
const handleEditModeChange = (value) => {
  // 检查是否为ClickHouse数据源，并显示特殊提示
  if (value && isClickhouseDataSource()) {
    ElMessage.warning('ClickHouse数据源仅支持添加和删除操作，不支持修改现有数据');
  } else if (value && !canEnableEditMode.value) {
    if (props.userPermissions && props.userPermissions.canRequestEdit) {
      requestEditPermissionVisible.value = true;
    } else {
      ElMessage.warning('您没有编辑权限，请联系管理员');
    }
    return;
  } else {
    // ElMessage.info(value ? '已开启编辑模式，双击单元格可以编辑数据' : '已关闭编辑模式');
  }

  // 根据当前活动的结果标签页更新对应的编辑模式状态
  console.log(`更新当前活动标签页 ${activeResultTab.value} 的编辑状态为: ${value}`);
  
  // 如果是主结果标签页
  if (activeResultTab.value === 'result') {
    resultTabsEditMode.value['main'] = value;

    // 强制触发响应式更新
    resultTabsEditMode.value = { ...resultTabsEditMode.value };
    console.log(`强制更新后的主结果标签编辑模式状态:`, resultTabsEditMode.value);
    
    // 更新主结果表
    nextTick(() => {
      const tableRef = getCurrentTableRef();
      if (tableRef) {
        console.log('更新主结果表的编辑状态');
        
        // 强制刷新主结果表
        if (tableRef.updateTableData) {
          if (props.tab.results && props.tab.results.length > 0) {
            // 使用分页数据而不是全部数据
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            const paginatedData = props.tab.results.slice(start, end);
            tableRef.updateTableData(paginatedData);
          } else {
            // 即使没有数据，也更新表格状态
            tableRef.updateTableData([]);
            console.log('主结果表没有数据，但仍然更新了编辑状态');
          }
        }
        
        // 强制更新表格数据引用，触发视图更新
        try {
          if (tableRef.tableData && Array.isArray(tableRef.tableData.value)) {
            tableRef.tableData.value = [...tableRef.tableData.value];
            console.log('已强制更新主表格视图');
          }
        } catch (err) {
          console.error('强制更新表格视图时出错:', err);
        }
      }
    });
  }
  // 如果是额外结果标签页，通过ID在数组中查找实际索引
  else if (activeResultTab.value.startsWith('result-tab-') || activeResultTab.value.startsWith('result-')) {
    // 在 resultTabs 数组中查找当前活动标签页的索引
    const resultTabsArray = props.tab.resultTabs || [];
    const index = resultTabsArray.findIndex(tab => tab.id === activeResultTab.value);

    if (index !== -1) {
      console.log(`设置标签页 ${activeResultTab.value} 的编辑模式状态，实际索引: ${index}`);
      resultTabsEditMode.value[index] = value;

      // 强制触发响应式更新
      resultTabsEditMode.value = { ...resultTabsEditMode.value };
      console.log(`强制更新后的编辑模式状态:`, resultTabsEditMode.value);
    } else {
      console.log(`未找到标签页 ${activeResultTab.value} 在数组中的索引，无法设置编辑模式`);
      return;
    }
    
    // 更新对应的结果表
    nextTick(() => {
      const tableRef = resultTableRefs.value[index];
      if (tableRef) {
        console.log(`更新结果标签 ${index} 的编辑状态`);

        // 强制刷新结果表
        const resultTabsArray = props.tab.resultTabs || [];
        if (tableRef.updateTableData && resultTabsArray[index]) {
          if (resultTabsArray[index].results && resultTabsArray[index].results.length > 0) {
            // 使用分页数据而不是全部数据
            const paginatedData = getResultTabPaginatedData(resultTabsArray[index]);
            tableRef.updateTableData(paginatedData);
          } else {
            // 即使没有数据，也更新表格状态
            tableRef.updateTableData([]);
            console.log(`结果标签 ${index} 没有数据，但仍然更新了编辑状态`);
          }
        }
        
        // 强制更新表格数据引用，触发视图更新
        try {
          if (tableRef.tableData && Array.isArray(tableRef.tableData.value)) {
            tableRef.tableData.value = [...tableRef.tableData.value];
            console.log(`已强制更新结果表格 ${index} 视图`);
          }
        } catch (err) {
          console.error(`强制更新结果表格 ${index} 视图时出错:`, err);
        }
      }
    });
  }
  
  // 发出事件通知父组件编辑模式状态变更，仅更新当前标签页
  // 只有当主结果标签页的编辑状态变化时才通知父组件
  if (activeResultTab.value === 'result') {
    emit('update:tab', {
      id: props.tab.id,
      results: props.tab.results,
      columns: props.tab.columns,
      resultTabs: props.tab.resultTabs,
      executed: props.tab.executed,
      messages: props.tab.messages,
      error: props.tab.error,
      rowCount: props.tab.rowCount,
      editModeEnabled: value
    });
  }
}

// 从顶部工具栏处理提交修改
const handleTopbarSubmitChanges = () => {
  console.log('🔥🔥🔥 handleTopbarSubmitChanges 被调用了！🔥🔥🔥');
  console.log('提交修改按钮被点击');
  
  // 确保用户有编辑权限
  if (!canEnableEditMode.value) {
    ElMessage.warning('您没有编辑权限，请联系管理员');
    return;
  }
  
  // 获取当前活动标签页的索引
  let tabIndex = 'main';
  if (activeResultTab.value === 'result') {
    tabIndex = 'main';
  } else if (activeResultTab.value.startsWith('result-tab-') || activeResultTab.value.startsWith('result-')) {
    // 通过ID在数组中查找实际索引
    const resultTabsArray = props.tab.resultTabs || [];
    tabIndex = resultTabsArray.findIndex(tab => tab.id === activeResultTab.value);
    if (tabIndex === -1) {
      console.warn(`未找到标签页 ${activeResultTab.value} 在数组中的索引`);
      return;
    }
  }
  console.log(`获取当前活动标签页 ${activeResultTab.value} 的索引: ${tabIndex}`);
  
  // 检查当前是否处于只读模式
  const isReadOnly = !(resultTabsEditMode.value[tabIndex] || false);
  console.log(`当前标签页 ${activeResultTab.value} 的编辑状态: ${!isReadOnly ? '可编辑' : '只读'}`);
  
  if (isReadOnly) {
    console.log('当前处于只读模式，正在切换到编辑模式');
    // 强制设置编辑模式为true
    resultTabsEditMode.value[tabIndex] = true;
    
    // 如果是主结果标签页，更新全局状态
    if (tabIndex === 'main') {
      currentEditModeEnabled.value = true;
      
      // 通知父组件
      emit('update:tab', {
        id: props.tab.id,
        results: props.tab.results,
        columns: props.tab.columns,
        resultTabs: props.tab.resultTabs,
        executed: props.tab.executed,
        messages: props.tab.messages,
        error: props.tab.error,
        rowCount: props.tab.rowCount,
        editModeEnabled: true
      });
    }
    
    // 等待DOM更新后再提交
    nextTick(() => {
      console.log('DOM已更新，准备提交修改');
      const tableRef = getCurrentTableRef();
      if (tableRef) {
        // 强制更新表格的只读状态
        if (tableRef.isReadOnly !== undefined) {
          tableRef.isReadOnly = false;
        }
        
        // 调用提交方法
        if (tableRef.handleSubmitChanges) {
          console.log('调用表格组件的handleSubmitChanges方法');

          // 调用提交方法（表格组件会在用户确认后立即退出编辑模式）
          tableRef.handleSubmitChanges();
          console.log('[handleTopbarSubmitChanges] 已调用表格组件的提交方法，表格组件会处理编辑模式退出');
        } else {
          console.warn('表格组件没有handleSubmitChanges方法');
        }
      } else {
        console.warn('无法获取当前表格引用');
      }
    });
  } else {
    // 如果已经是编辑模式，直接提交
    console.log('当前已处于编辑模式，直接提交修改');
    const tableRef = getCurrentTableRef();
    if (tableRef && tableRef.handleSubmitChanges) {
      // 调用提交方法（表格组件会在用户确认后立即退出编辑模式）
      tableRef.handleSubmitChanges();
      console.log('[handleTopbarSubmitChanges] 已调用表格组件的提交方法，表格组件会处理编辑模式退出');
    } else {
      console.warn('无法获取表格引用或表格组件没有handleSubmitChanges方法');
    }
  }
}

// 从顶部工具栏处理添加行
const handleTopbarAddRow = () => {
  // 根据当前活动的标签页，获取对应的表格组件并调用其添加行方法
  const tableRef = getCurrentTableRef();
  if (tableRef && tableRef.handleAddRow) {
    tableRef.handleAddRow();
  }
}

// 从顶部工具栏处理删除选中行
const handleTopbarDeleteSelected = () => {
  console.log('=== 删除选中行调试信息 ===');
  console.log('当前活动标签页:', activeResultTab.value);
  console.log('selectedRowsCount:', selectedRowsCount.value);
  console.log('hasSelectedRows:', hasSelectedRows.value);

  // 根据当前活动的标签页，获取对应的表格组件并调用其删除方法
  const tableRef = getCurrentTableRef();
  console.log('获取到的表格引用:', tableRef);

  if (tableRef) {
    console.log('表格引用的selectedRowKeys:', tableRef.selectedRowKeys);
    console.log('表格引用的selectedRowKeys大小:', tableRef.selectedRowKeys?.size || 0);

    if (tableRef.handleDeleteSelected) {
      console.log('调用表格的handleDeleteSelected方法');
      tableRef.handleDeleteSelected();
    } else {
      console.warn('表格引用没有handleDeleteSelected方法');
    }
  } else {
    console.error('无法获取表格引用');
  }
}

// 请求编辑权限
const submitEditPermissionRequest = () => {
  emit('request-edit-permission', {
    tabId: props.tab.id,
    datasourceId: selectedDatasourceId.value,
    schema: selectedSchema.value,
    reason: editPermissionReason.value
  });
  
  requestEditPermissionVisible.value = false;
  editPermissionReason.value = '';
  ElMessage.success('申请已提交，请等待管理员审核');
}

// 开始编辑单元格
const startEditing = () => {
  // 如果没有开启编辑模式，显示提示但仍然显示详情弹窗
  if (!currentEditModeEnabled.value) {
    ElMessage.warning('请先开启编辑模式，否则只能查看但不能编辑');
    return;
  }
  
  console.log('开始编辑单元格', {
    cellDetailTitle: cellDetailTitle.value,
    cellDetailContent: cellDetailContent.value,
    activeResultTab: activeResultTab.value
  });
  
  // 提取列名用于后续处理
  const processedColumnName = cellDetailTitle.value.replace('查看', '').replace('编辑', '').replace(':', '').trim();
  console.log('处理后的列名:', processedColumnName);
  
  // 如果还没有编辑上下文，先构建一个
  if (!editingCellContext.value) {
    // 获取当前行数据
    // 更改点：优先使用 props.tab.currentEditingRow
    const currentEditRow = props.tab.currentEditingRow;
    if (!currentEditRow) {
      // 当没有 currentEditingRow 时，尝试从当前打开的单元格对话框获取信息
      if (!cellDetailVisible.value || !processedColumnName || cellDetailContent.value === undefined) {
        ElMessage.error('无法确定要编辑的行，请重新选择单元格');
        console.error('更新失败：无法确定要编辑的行，且没有足够的上下文信息');
        isEditing.value = false;
        cellDetailVisible.value = false;
        return;
      }
      
      console.warn('找不到 currentEditingRow，尝试从活动结果标签页恢复行数据');
      
      // 尝试从当前激活的结果标签页获取数据
      let resultRows = null;
      if (activeResultTab.value === 'result' && props.tab.results) {
        resultRows = props.tab.results;
      } else if (activeResultTab.value.startsWith('result-tab-') && props.tab.resultTabs) {
        const tabIndex = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
        if (props.tab.resultTabs[tabIndex] && props.tab.resultTabs[tabIndex].results) {
          resultRows = props.tab.resultTabs[tabIndex].results;
        }
      }
      
      // 如果找到了结果集，尝试匹配当前编辑的单元格
      if (resultRows && resultRows.length > 0) {
        console.log('从结果集恢复行数据，结果集大小:', resultRows.length);
        // 由于没有行标识符，只能根据单元格值推测是哪一行
        const matchingRows = resultRows.filter(row => 
          row[processedColumnName] !== undefined && 
          row[processedColumnName] === cellDetailContent.value
        );
        
        if (matchingRows.length === 1) {
          // 找到唯一匹配的行
          console.log('找到匹配的行数据:', matchingRows[0]);
          
          // 构建编辑上下文
          editingCellContext.value = {
            row: matchingRows[0],
            column: processedColumnName,
            value: cellDetailContent.value
          };
          console.log('已使用恢复的行数据构建编辑上下文:', editingCellContext.value);
        } else if (matchingRows.length > 1) {
          console.warn('找到多个匹配行，无法确定要编辑的具体行');
          return;
        } else {
          ElMessage.error('无法确定要编辑的行，请重新选择单元格');
          console.error('更新失败：无法确定要编辑的行');
          isEditing.value = false;
          cellDetailVisible.value = false;
          return;
        }
      } else {
        ElMessage.error('无法确定要编辑的行，请重新选择单元格');
        console.error('更新失败：无法确定要编辑的行');
        isEditing.value = false;
        cellDetailVisible.value = false;
        return;
      }
    } else {
      // 构建编辑上下文
      editingCellContext.value = {
        row: currentEditRow,
        column: processedColumnName,
        value: cellDetailContent.value
      };
      console.log('已构建编辑上下文:', editingCellContext.value);
    }
  }
  
  // 先设置编辑值，再设置编辑状态
  cellEditValue.value = cellDetailContent.value;
  isEditing.value = true;
  
  // 使用setTimeout确保DOM已经更新
  setTimeout(() => {
    // 尝试聚焦到输入框
    const inputRef = document.querySelector('.cell-edit-content .el-input__inner');
    if (inputRef) {
      console.log('找到输入框元素，尝试聚焦');
      inputRef.focus();
    } else {
      console.log('未找到输入框元素');
    }
  }, 100);
}

// 取消编辑
const cancelEditing = () => {
  isEditing.value = false;
  cellDetailVisible.value = false; // 直接关闭弹窗
}

// 提交编辑
const submitEdit = () => {
  console.log('保存修改按钮点击');

  // 确保编辑值已设置
  if (cellEditValue.value === undefined || cellEditValue.value === null) {
    cellEditValue.value = cellDetailContent.value;
  }

  console.log('提交编辑 - 开始', {
    cellEditValue: cellEditValue.value,
    cellDetailContent: cellDetailContent.value,
    cellDetailTitle: cellDetailTitle.value,
    isEditing: isEditing.value,
    editingCellContext: editingCellContext.value
  });

  // 如果不在编辑状态，直接返回
  if (!isEditing.value) {
    console.warn('不在编辑状态，无法提交修改');
    return;
  }

  // 直接使用当前值创建一个简单的编辑上下文
  const processedColumnName = cellDetailTitle.value.replace('编辑', '').replace(':', '').trim();
  console.log('处理后的列名:', processedColumnName);

  // 如果还没有编辑上下文，先构建一个
  if (!editingCellContext.value) {
    // 获取当前行数据
    // 更改点：优先使用 props.tab.currentEditingRow
    const currentEditRow = props.tab.currentEditingRow;
    if (!currentEditRow) {
      // 当没有 currentEditingRow 时，尝试从当前打开的单元格对话框获取信息
      if (!cellDetailVisible.value || !processedColumnName || cellDetailContent.value === undefined) {
        ElMessage.error('无法确定要编辑的行，请重新选择单元格');
        console.error('更新失败：无法确定要编辑的行，且没有足够的上下文信息');
        isEditing.value = false;
        cellDetailVisible.value = false;
        return;
      }
      
      console.warn('找不到 currentEditingRow，尝试从活动结果标签页恢复行数据');
      
      // 尝试从当前激活的结果标签页获取数据
      let resultRows = null;
      if (activeResultTab.value === 'result' && props.tab.results) {
        resultRows = props.tab.results;
      } else if (activeResultTab.value.startsWith('result-tab-') && props.tab.resultTabs) {
        const tabIndex = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
        if (props.tab.resultTabs[tabIndex] && props.tab.resultTabs[tabIndex].results) {
          resultRows = props.tab.resultTabs[tabIndex].results;
        }
      }
      
      // 如果找到了结果集，尝试匹配当前编辑的单元格
      if (resultRows && resultRows.length > 0) {
        console.log('从结果集恢复行数据，结果集大小:', resultRows.length);
        // 由于没有行标识符，只能根据单元格值推测是哪一行
        const matchingRows = resultRows.filter(row => 
          row[processedColumnName] !== undefined && 
          row[processedColumnName] === cellDetailContent.value
        );
        
        if (matchingRows.length === 1) {
          // 找到唯一匹配的行
          console.log('找到匹配的行数据:', matchingRows[0]);
          
          // 构建编辑上下文
          editingCellContext.value = {
            row: matchingRows[0],
            column: processedColumnName,
            value: cellDetailContent.value
          };
          console.log('已使用恢复的行数据构建编辑上下文:', editingCellContext.value);
          return;
        } else if (matchingRows.length > 1) {
          console.warn('找到多个匹配行，无法确定要编辑的具体行');
        }
      }
      
      ElMessage.error('无法确定要编辑的行，请重新选择单元格');
      console.error('更新失败：无法确定要编辑的行');
      isEditing.value = false;
      cellDetailVisible.value = false;
      return;
    }

    // 构建编辑上下文
    editingCellContext.value = {
      row: currentEditRow,
      column: processedColumnName,
      value: cellDetailContent.value
    };
    console.log('已构建编辑上下文:', editingCellContext.value);
  }

  const { row, column, value } = editingCellContext.value;

  // 检查列名是否有效
  if (!column || column === 'undefined' || typeof column === 'undefined') {
    ElMessage.error('无效的字段名');
    console.error('更新失败：字段名无效或未定义', { column });
    isEditing.value = false;
    return;
  }

  // 检查值是否有变化
  if (cellEditValue.value === value) {
    ElMessage.info('值未变化，无需更新');
    isEditing.value = false;
    return;
  }

  // 从SQL中提取表名
  const dbTableName = extractTableName();

  // 检查表名是否存在
  if (!dbTableName) {
    ElMessage.error('无法确定要更新的表名，请确保SQL中包含表名');
    console.error('更新失败：无法从SQL中提取表名', { sql: sqlContent.value });
    isEditing.value = false;
    return;
  }

  // 检查是否为ClickHouse数据源
  if (isClickhouseDataSource()) {
    ElMessage.error('ClickHouse数据源不支持修改现有数据，仅支持添加和删除操作');
    isEditing.value = false;
    cellDetailVisible.value = false;
    return;
  }

  console.log('准备提交更新:', {
    表名: dbTableName,
    字段: column,
    旧值: value,
    新值: cellEditValue.value,
    数据源: selectedDatasourceId.value,
    库: selectedSchema.value
  });

  // 创建一个清理过的row对象，排除Vue内部属性
  const rowData = row; // 使用已有的row对象
  // 创建清理过的行数据
  const processedRow = {};
  for (const key in rowData) {
    // 排除Vue内部属性
    if (!key.startsWith('__v') && key !== '_rowKey' && typeof rowData[key] !== 'function') {
      processedRow[key] = rowData[key];
    }
  }

  console.log('原始行数据:', rowData);
  console.log('清理后的行数据:', processedRow);

  // 已完成编辑，重置状态
  isEditing.value = false;

  // 关闭对话框
  cellDetailVisible.value = false;
  
  // 更新表格中的对应单元格
  // 尝试获取当前活动的表格引用
  let currentTableRef = getCurrentTableRef();
  console.log('currentTableRef', currentTableRef);
  
  // 如果无法获取表格引用，尝试重新获取
  if (!currentTableRef) {
    console.warn('第一次尝试获取表格引用失败，正在尝试强制刷新引用');
    
    // 尝试刷新resultTableRefs
    if (resultTableRefs.value && Array.isArray(resultTableRefs.value)) {
      console.log('当前resultTableRefs长度:', resultTableRefs.value.length);
      
      // 确保resultTableRefs数组长度与resultTabs同步
      if (resultTabs.value && resultTabs.value.length > 0) {
        while (resultTableRefs.value.length < resultTabs.value.length) {
          resultTableRefs.value.push(undefined);
        }
        console.log('已调整resultTableRefs长度:', resultTableRefs.value.length);
      }
    } else {
      // 如果resultTableRefs不是数组，重新初始化
      resultTableRefs.value = [];
      console.log('重新初始化resultTableRefs为空数组');
    }
    
    // 延迟重新获取表格引用
    setTimeout(() => {
      currentTableRef = getCurrentTableRef();
      console.log('延迟后重新获取表格引用:', currentTableRef);
      updateCellValueWithRef(currentTableRef);
    }, 100);
  } else {
    // 直接使用获取到的表格引用
    updateCellValueWithRef(currentTableRef);
  }
  
  // 封装更新单元格的逻辑为函数，方便复用
  function updateCellValueWithRef(tableRef) {
    if (tableRef) {
      try {
        // 调用表格组件的更新单元格方法
        tableRef.handleUpdateCellValue({
          type: 'update-cell',
          data: {
            row: rowData,
            columnKey: column,
            value: cellEditValue.value,
            originalValue: value
          }
        });

        // 确保表格数据更新
        nextTick(() => {
          if (tableRef && tableRef.tableData) {
            try {
              // 检查tableData.value是否可迭代
              if (tableRef.tableData.value &&
                  typeof tableRef.tableData.value[Symbol.iterator] === 'function') {
                // 创建新数组引用以触发视图更新
                tableRef.tableData.value = [...tableRef.tableData.value];
                console.log('已强制更新表格数据视图');
              } else {
                console.warn('tableData.value不是可迭代对象，无法使用扩展运算符');
                // 如果不可迭代，尝试其他方式刷新视图
                if (tableRef.refreshTable) {
                  tableRef.refreshTable();
                }
              }
            } catch (err) {
              console.error('更新表格数据视图时出错:', err);
            }
          }

          // 显示成功消息
          ElMessage.success('已保存修改，点击"提交修改"完成更新');
        });
      } catch (error) {
        console.error('更新单元格值失败回调:', error);
        ElMessage.error('更新单元格失败: ' + error.message);
      }
    } else {
      console.error('找不到表格组件引用');
      
      // 尝试直接更新原始数据
      try {
        // 直接更新行数据
        if (rowData && column) {
          rowData[column] = cellEditValue.value;
          console.log('已直接更新行数据:', { column, value: cellEditValue.value });
          
          // 尝试更新当前结果集中的数据
          updateResultsData(rowData, column);
          
          ElMessage.success('已直接更新数据，请在底部操作栏点击"提交修改"完成更新');
        } else {
          ElMessage.error('无法更新表格，请尝试切换到对应的结果标签页后重试');
        }
      } catch (directUpdateError) {
        console.error('直接更新数据失败:', directUpdateError);
        ElMessage.error('无法更新表格，请尝试切换到对应的结果标签页后重试');
      }
    }
  }
  
  // 直接更新结果集数据的辅助函数
  function updateResultsData(rowData, columnKey) {
    // 尝试在当前活动的结果集中查找并更新数据
    if (activeResultTab.value === 'result' && props.tab.results) {
      const index = props.tab.results.findIndex(r => 
        Object.keys(r).every(key => r[key] === rowData[key] || 
          (key === columnKey && r[key] === value))
      );
      
      if (index !== -1) {
        props.tab.results[index][columnKey] = cellEditValue.value;
        console.log(`已更新主结果集中索引${index}的数据`);
        
        // 强制更新视图
        props.tab.results = [...props.tab.results];
        emit('update:tab', {
          id: props.tab.id,
          results: props.tab.results,
          columns: props.tab.columns,
          resultTabs: props.tab.resultTabs,
          executed: props.tab.executed,
          messages: props.tab.messages,
          error: props.tab.error,
          rowCount: props.tab.rowCount,
          editModeEnabled: props.tab.editModeEnabled
        });
      }
    } 
    // 处理额外结果标签页
    else if (activeResultTab.value.startsWith('result-tab-') && props.tab.resultTabs) {
      const tabIndex = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
      if (tabIndex >= 0 && props.tab.resultTabs[tabIndex] && props.tab.resultTabs[tabIndex].results) {
        const results = props.tab.resultTabs[tabIndex].results;
        const index = results.findIndex(r => 
          Object.keys(r).every(key => r[key] === rowData[key] || 
            (key === columnKey && r[key] === value))
        );
        
        if (index !== -1) {
          results[index][columnKey] = cellEditValue.value;
          console.log(`已更新额外结果集${tabIndex}中索引${index}的数据`);
          
          // 强制更新视图
          props.tab.resultTabs[tabIndex].results = [...results];
          emit('update:tab', {
            id: props.tab.id,
            results: props.tab.results,
            columns: props.tab.columns,
            resultTabs: props.tab.resultTabs,
            executed: props.tab.executed,
            messages: props.tab.messages,
            error: props.tab.error,
            rowCount: props.tab.rowCount,
            editModeEnabled: props.tab.editModeEnabled
          });
        }
      }
    }
  }
};

// =====================================================================
// 2. 在 EditableResultTable.vue 文件中，确保以下代码存在于 handleUpdateCellValue 函数中：
// =====================================================================

/*
// 添加处理update-cell-value事件的函数

*/ 

// 从SQL中提取表名（简单实现，实际可能需要更复杂的SQL解析）
const extractTableName = (customSql) => {
  // 使用提供的SQL或默认使用编辑器中的SQL
  const sql = (customSql || sqlContent.value || '').toLowerCase();
  
  if (!sql) {
    console.warn('SQL为空，无法提取表名');
    return '';
  }
  
  console.log('尝试从SQL提取表名:', sql.substring(0, 100) + (sql.length > 100 ? '...' : ''));
  
  // 处理反引号和引号包围的表名
  const tableNamePattern = /[\`\"\[\']?([a-z0-9_]+)[\`\"\[\']?/;
  
  // 尝试匹配 FROM 后面的表名 (处理FROM子句更严格的格式)
  // 增强正则表达式，支持带反引号的库名和表名格式，并处理带点号的表名
  const fromMatch = sql.match(/from\s+((`[^`]+`|[a-z0-9_]+)\.)?(`[^`]+`|[a-z0-9_]+)(\s|$|;|\)|WHERE|JOIN|GROUP|ORDER|HAVING|LIMIT)/i);
  if (fromMatch) {
    // 提取表名部分，优先使用第3个捕获组(表名)
    let tableName = fromMatch[3] || '';
    
    // 移除可能的反引号
    tableName = tableName.replace(/[`"'\[\]]/g, '');
    
    console.log('从FROM子句提取到表名:', tableName, '(原始匹配:', fromMatch[0], ')');
    
    // 更新父组件的tab对象
    updateCurrentTableName(tableName);
    
    console.log(`提取到的表名: ${tableName} (来源: FROM子句)`);
    return tableName;
  }
  
  // 尝试匹配 UPDATE 后面的表名
  const updateMatch = sql.match(/update\s+((`[^`]+`|[a-z0-9_]+)\.)?(`[^`]+`|[a-z0-9_]+)(\s|$|\;)/i);
  if (updateMatch) {
    // 提取表名部分
    let tableName = updateMatch[3] || '';
    
    // 移除可能的反引号
    tableName = tableName.replace(/[`"'\[\]]/g, '');
    
    console.log('从UPDATE子句提取到表名:', tableName, '(原始匹配:', updateMatch[0], ')');
    
    // 更新父组件的tab对象
    updateCurrentTableName(tableName);
    
    return tableName;
  }
  
  // 尝试匹配 INSERT INTO 后面的表名
  const insertMatch = sql.match(/insert\s+into\s+((`[^`]+`|[a-z0-9_]+)\.)?(`[^`]+`|[a-z0-9_]+)(\s|$|\;|\()/i);
  if (insertMatch) {
    // 提取表名部分
    let tableName = insertMatch[3] || '';
    
    // 移除可能的反引号
    tableName = tableName.replace(/[`"'\[\]]/g, '');
    
    console.log('从INSERT INTO子句提取到表名:', tableName, '(原始匹配:', insertMatch[0], ')');
    
    // 更新父组件的tab对象
    updateCurrentTableName(tableName);
    
    return tableName;
  }
  
  // 尝试匹配 DELETE FROM 后面的表名
  const deleteMatch = sql.match(/delete\s+from\s+((`[^`]+`|[a-z0-9_]+)\.)?(`[^`]+`|[a-z0-9_]+)(\s|$|\;|\()/i);
  if (deleteMatch) {
    // 提取表名部分
    let tableName = deleteMatch[3] || '';
    
    // 移除可能的反引号
    tableName = tableName.replace(/[`"'\[\]]/g, '');
    
    console.log('从DELETE FROM子句提取到表名:', tableName, '(原始匹配:', deleteMatch[0], ')');
    
    // 更新父组件的tab对象
    updateCurrentTableName(tableName);
    
    return tableName;
  }
  
  // 最后尝试匹配任何带有SELECT的第一个表名
  if (sql.includes('select')) {
    // 尝试找到从FROM开始的部分
    const fromPart = sql.split(/\bfrom\b/i)[1];
    if (fromPart) {
      // 使用增强的正则表达式提取表名
      const firstTableMatch = fromPart.match(/\s*((`[^`]+`|[a-z0-9_]+)\.)?(`[^`]+`|[a-z0-9_]+)/i);
      if (firstTableMatch) {
        // 提取表名部分
        let tableName = firstTableMatch[3] || '';
        
        // 移除可能的反引号
        tableName = tableName.replace(/[`"'\[\]]/g, '');
        
        console.log('从SELECT子句提取到表名:', tableName, '(原始匹配:', firstTableMatch[0], ')');
        
        // 更新父组件的tab对象
        updateCurrentTableName(tableName);
        
        return tableName;
      }
    }
  }
  
  // 如果无法提取，返回空字符串而不是'undefined'
  console.warn('未能从SQL中提取表名:', sqlContent.value);
  return '';
}

// 添加处理单元格值更新的函数
const handleUpdateCellValue = (eventData) => {
  console.log('收到单元格更新事件:', eventData);
  
  // 如果编辑器组件引用存在，直接调用组件的处理函数
  const tableRef = getCurrentTableRef();
  if (tableRef) {
    console.log('转发更新事件到 EditableResultTable 组件');
    tableRef.handleUpdateCellValue(eventData);
    
    // 确保在下一次更新循环中检查状态
    nextTick(() => {
      if (tableRef.hasPendingChanges) {
        console.log('单元格更新后有待提交的变更');
      } else {
        console.log('单元格更新后没有待提交的变更，可能需要手动添加');
        // 如果没有变更被添加，可能需要手动添加
        if (eventData && eventData.data) {
          const { row, columnKey, value, originalValue } = eventData.data;
          
          // 确保行有__v_key属性
          let rowKey = null;
          if (row.__v_key !== undefined) {
            rowKey = row.__v_key;
            console.log('使用现有的__v_key:', rowKey);
          } else {
            // 如果行没有__v_key，尝试在表格数据中查找匹配的行
            if (tableRef.tableData) {
              const matchedRow = tableRef.tableData.find(r => r.id === row.id);
              if (matchedRow && matchedRow.__v_key !== undefined) {
                rowKey = matchedRow.__v_key;
                console.log('找到匹配行的__v_key:', rowKey);
              }
            }
            
            // 如果还是找不到，生成一个临时的键
            if (!rowKey) {
              rowKey = Date.now() + '_' + Math.random().toString(36).substr(2, 9);
              console.log('生成临时rowKey:', rowKey);
              row.__v_key = rowKey; // 为原始行添加__v_key属性
            }
          }
          
          tableRef.addToPendingChanges({
            type: 'update',
            row,
            rowKey: rowKey,
            columnKey,
            columnName: columnKey,
            value,
            originalValue,
            timestamp: Date.now()
          });
          
          // 强制更新表格数据
          if (tableRef.tableData) {
            try {
              // 检查tableData.value是否可迭代
              if (tableRef.tableData.value && 
                  typeof tableRef.tableData.value[Symbol.iterator] === 'function') {
                tableRef.tableData.value = [...tableRef.tableData.value];
                console.log('强制刷新表格数据以确保视图更新');
              } else {
                console.warn('tableData.value不是可迭代对象，无法使用扩展运算符');
                // 如果不可迭代，尝试其他方式刷新视图
                if (tableRef.refreshTable) {
                  tableRef.refreshTable();
                }
              }
            } catch (err) {
              console.error('刷新表格数据时出错:', err);
            }
          }
        }
      }
    });
  } else {
    console.error('找不到 EditableResultTable 组件引用');
  }
};

// 处理待提交变更更新事件
const handlePendingChangesUpdated = (changes) => {
  console.log('收到待提交变更更新事件，变更数量:', changes.length);
  
  // 更新 UI 或状态
  if (changes.length > 0) {
    // 强制渲染底部操作栏
    nextTick(() => {
      // 向父组件传递状态变更
      emit('update:tab', {
        id: props.tab.id,
        results: props.tab.results,
        columns: props.tab.columns,
        resultTabs: props.tab.resultTabs,
        executed: props.tab.executed,
        messages: props.tab.messages,
        error: props.tab.error,
        rowCount: props.tab.rowCount,
        editModeEnabled: props.tab.editModeEnabled,
        hasPendingChanges: true,
        pendingChangesCount: changes.length
      });
      
      console.log('已通知父组件待提交变更状态更新，数量:', changes.length);
    });
  }
};

// 更新父组件中的当前表名
const updateCurrentTableName = (tableName) => {
  if (!tableName || typeof tableName !== 'string') return;
  
  console.log('更新当前表名:', tableName);
  
  // 根据当前活动的结果标签页更新表名
  if (activeResultTab.value === 'result' && props.tab.id) {
    // 通过emit更新父组件中的tab对象
    emit('update:tab', {
      id: props.tab.id,
      results: props.tab.results,
      columns: props.tab.columns,
      resultTabs: props.tab.resultTabs,
      executed: props.tab.executed,
      messages: props.tab.messages,
      error: props.tab.error,
      rowCount: props.tab.rowCount,
      editModeEnabled: props.tab.editModeEnabled,
      currentQueryTableName: tableName
    });
    
    // 直接更新本地的tab对象，确保立即生效
    props.tab.currentQueryTableName = tableName;
    console.log(`主结果标签页的表名已设置为: ${tableName}`);
  } else if (activeResultTab.value.startsWith('result-')) {
    const index = parseInt(activeResultTab.value.split('-')[1]);
    if (resultTabs.value[index]) {
      resultTabs.value[index].currentQueryTableName = tableName;
      console.log(`额外结果标签页${index+2}的表名已设置为: ${tableName}`);
    }
  }
}

// 获取单元格输入类型
const getCellInputType = () => {
  if (!cellDetailContent.value) return 'text';
  
  // 如果内容很长，使用textarea
  if (cellDetailContent.value.length > 100) {
    return 'textarea';
  }
  
  // 尝试判断是否为数字
  if (!isNaN(cellDetailContent.value)) {
    return 'text'; // 使用text而非number，以支持大整数
  }
  
  // 尝试判断是否为日期
  if (/^\d{4}-\d{2}-\d{2}/.test(cellDetailContent.value)) {
    // 为避免日期解析问题，还是使用文本输入
    return 'text';
  }
  
  // 默认为文本
  return 'text';
}

// 处理单元格更新
const handleCellUpdate = ({ row, column, newValue, originalValue }) => {
  console.log('单元格更新:', { row, column, newValue, originalValue });
  
  // 检查column是否有效
  if (!column || column === 'undefined' || typeof column === 'undefined') {
    ElMessage.error('无效的字段名');
    console.error('更新失败：字段名无效或未定义', { column });
    return;
  }
  
  // 从SQL中提取表名
  const tableName = extractTableName();
  
  // 检查表名是否存在
  if (!tableName) {
    ElMessage.error('无法确定要更新的表名，请确保SQL中包含表名');
    console.error('更新失败：无法从SQL中提取表名', { sql: sqlContent.value });
    return;
  }
  
  console.log('准备通过组件接口更新数据:', {
    表名: tableName,
    字段: column,
    新值: newValue,
    原值: originalValue,
    数据源: selectedDatasourceId.value,
    库: selectedSchema.value
  });
  
  // 发出事件通知父组件
  emit('update-cell-value', {
    datasourceId: selectedDatasourceId.value,
    schema: selectedSchema.value,
    tableName: tableName,
    row,
    column,
    columnName: column, // 明确添加columnName字段确保和API参数匹配
    columnKey: column, // 同时添加columnKey字段，确保与其他地方的参数一致
    newValue,
    originalValue
  });
  
  ElMessage.success(`字段 ${column} 已更新为: ${newValue}`);
};

// 处理删除行
const handleDeleteRow = ({ row, index }) => {
  console.log('删除行:', { row, index });
  
  // 从SQL中提取表名
  const tableName = extractTableName();
  
  // 检查表名是否存在
  if (!tableName) {
    ElMessage.error('无法确定要操作的表名，请确保SQL中包含表名');
    console.error('删除失败：无法从SQL中提取表名', { sql: sqlContent.value });
    return;
  }
  
  console.log('准备删除数据行:', {
    表名: tableName,
    行索引: index,
    数据源: selectedDatasourceId.value,
    库: selectedSchema.value
  });
  
  // 发出事件通知父组件
  emit('delete-row', {
    datasourceId: selectedDatasourceId.value,
    database: selectedSchema.value,  // 修改为 database
    schema: selectedSchema.value,
    tableName: tableName,
    row,
    index
  });
  
  ElMessage.success('删除请求已提交');
};
// 处理添加行
const handleAddRow = async ({ row }) => {
  if (!editModeEnabled.value) {
    ElMessage.warning('请先开启编辑模式');
    return;
  }

  const tableName = extractTableName();
  if (!tableName) {
    ElMessage.error('无法确定要操作的表名，请确保SQL中包含表名');
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '正在添加...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 调用新增API
    await addTableRow({
      datasourceId: selectedDatasourceId.value,
      database: selectedSchema.value,  // 修改为 database
      schema: selectedSchema.value,
      tableName: tableName,
      row: row
    });

    // 添加成功后重新执行查询
    await handleExecuteQuery();
    ElMessage.success('数据已添加');
  } catch (error) {
    console.error('添加失败:', error);
    ElMessage.error(error.response?.data?.message || '添加失败，请重试');
  } finally {
    loading.close();
  }
};

// 判断是否为DDL操作
const isDDLOperation = () => {
  return isDDL(sqlContent.value);
};

// 获取空结果描述
const getEmptyResultDescription = () => {
  if (!sqlContent.value) return "请输入SQL语句";
  
  // 检查是否为DDL语句
  if (isDDLOperation()) {
    // 如果有自定义消息，优先显示
    if (props.tab.messages && props.tab.messages.includes('执行成功')) {
      return "操作执行成功";
    }
    return "SQL语句已执行成功";
  }
  
  // 普通查询语句
  return "查询结果为空，未找到任何数据";
}

// 清空执行输出
const clearExecutionOutput = () => {
  props.tab.executionLogs = [];
  // 通知父组件标签页的执行日志已更新
  emit('update:tab', {
    id: props.tab.id,
    results: props.tab.results,
    columns: props.tab.columns,
    resultTabs: props.tab.resultTabs,
    executed: props.tab.executed,
    messages: props.tab.messages,
    error: props.tab.error,
    rowCount: props.tab.rowCount,
    editModeEnabled: props.tab.editModeEnabled,
    executionLogs: []
  });
}

// 关闭结果标签页（切换到输出标签）
const closeResultTab = (e) => {
  e.preventDefault();
  activeResultTab.value = 'output';
}

// 获取当前格式化的时间戳
const getCurrentTimestamp = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  
  return `[${year}-${month}-${day} ${hours}:${minutes}:${seconds}]`;
}

  // 添加执行日志（已简化，因为不再需要输出面板）
const addExecutionLog = (content, status = 'success') => {
  // 仅记录日志到控制台，不再显示到UI
  // console.log(`[执行日志] ${status}: ${content}`);
};

// 已移除更新SQL执行结果的功能，因为不再需要输出面板
const updateSqlExecutionResult = (resultContent, status = 'success') => {
  // 仅记录日志到控制台
  console.log(`[SQL执行结果] ${status}: ${resultContent}`);
  return true;
};

// 已移除处理点击输出标签的事件，因为不再需要输出面板

// 截断SQL语句，用于显示在结果标签页
const truncateSql = (sql, maxLength) => {
  if (!sql) return '';
  if (sql.length <= maxLength) return sql;
  return sql.substring(0, maxLength) + '...';
};

// 确保 resultTab 有正确的分页属性
const ensureResultTabPagination = (resultTab) => {
  if (!resultTab) return;

  // 确保分页属性存在且有效
  if (!resultTab.currentPage || resultTab.currentPage < 1) {
    resultTab.currentPage = 1;
  }
  if (!resultTab.pageSize || resultTab.pageSize < 1) {
    resultTab.pageSize = 20;
  }

  console.log(`确保分页属性: ${resultTab.id} - currentPage=${resultTab.currentPage}, pageSize=${resultTab.pageSize}`);
};

// 获取结果标签页的分页数据
const getResultTabPaginatedData = (resultTab) => {
  console.log('[getResultTabPaginatedData] 输入参数:', {
    resultTabId: resultTab?.id,
    resultsLength: resultTab?.results?.length,
    isArray: Array.isArray(resultTab?.results)
  });
  
  if (!resultTab || !resultTab.results || !Array.isArray(resultTab.results)) {
    console.log('[getResultTabPaginatedData] 分页数据获取失败: 无效的resultTab或results');
    return [];
  }

  // 确保分页属性正确初始化
  ensureResultTabPagination(resultTab);

  // 确保分页参数有默认值
  const currentPage = resultTab.currentPage || 1;
  const pageSize = resultTab.pageSize || 20;

  const start = (currentPage - 1) * pageSize;
  const end = start + pageSize;

  console.log(`分页数据计算: 总数=${resultTab.results.length}, 当前页=${currentPage}, 每页=${pageSize}, 开始=${start}, 结束=${end}`);

  // 创建深拷贝以避免行对象共享引用的问题
  // 这确保每个表格实例都有独立的数据，编辑一行不会影响其他行
  const slicedData = resultTab.results.slice(start, end);
  console.log(`分页数据切片结果: 切片长度=${slicedData.length}`);

  const finalData = slicedData.map(row => {
    if (!row || typeof row !== 'object') {
      return row; // 如果不是对象，直接返回
    }

    // 创建行对象的深拷贝
    const clonedRow = {};
    for (const key in row) {
      if (row.hasOwnProperty(key)) {
        const value = row[key];
        // 对于简单值类型，直接复制
        if (value === null || value === undefined || typeof value !== 'object') {
          clonedRow[key] = value;
        } else {
          // 对于对象类型，进行深拷贝
          try {
            clonedRow[key] = JSON.parse(JSON.stringify(value));
          } catch (e) {
            // 如果JSON序列化失败，回退到浅拷贝
            clonedRow[key] = value;
          }
        }
      }
    }

    return clonedRow;
  });

  console.log(`最终返回的分页数据长度: ${finalData.length}`);
  console.log('最终返回的分页数据前3条:', finalData.slice(0, 3));

  return finalData;
};

// 处理结果标签页的分页大小变化
const handleResultTabSizeChange = (resultTab, size) => {
  resultTab.pageSize = size;
  resultTab.currentPage = 1; // 重置为第一页
  // 强制更新表格数据
  nextTick(() => {
    const index = props.tab.resultTabs.findIndex(tab => tab.id === resultTab.id);
    if (index !== -1 && resultTableRefs.value[index]) {
      const tableRef = resultTableRefs.value[index];
      if (tableRef.updateTableData) {
        tableRef.updateTableData(getResultTabPaginatedData(resultTab));
      }
    }
  });
};

// 处理结果标签页的页码变化
const handleResultTabPageChange = (resultTab, page) => {
  resultTab.currentPage = page;
  // 强制更新表格数据
  nextTick(() => {
    const index = props.tab.resultTabs.findIndex(tab => tab.id === resultTab.id);
    if (index !== -1 && resultTableRefs.value[index]) {
      const tableRef = resultTableRefs.value[index];
      if (tableRef.updateTableData) {
        tableRef.updateTableData(getResultTabPaginatedData(resultTab));
      }
    }
  });
};

// 关闭指定的结果标签页
const closeResultTabById = (tabId) => {
  console.log(`关闭标签页: ${tabId}`);
  
  // 使用实际的标签页ID
  if (tabId.startsWith('result-tab-')) {
    // 在resultTabs数组中找到对应的索引
    const tabIndex = props.tab.resultTabs.findIndex(tab => tab.id === tabId);
    if (tabIndex === -1) {
      console.warn(`未找到标签页ID: ${tabId}`);
      return;
    }
    
    console.log(`找到标签页索引: ${tabIndex}, 标签页ID: ${tabId}`);
    
    // 通知父组件移除结果标签页
    const updatedResultTabs = [...props.tab.resultTabs];
    updatedResultTabs.splice(tabIndex, 1);
    
    // 更新父组件中的tab对象
    emit('update:tab', {
      id: props.tab.id,
      results: props.tab.results,
      columns: props.tab.columns,
      resultTabs: updatedResultTabs,
      executed: props.tab.executed,
      messages: props.tab.messages,
      error: props.tab.error,
      rowCount: props.tab.rowCount,
      editModeEnabled: props.tab.editModeEnabled
    });
    
    // 如果关闭的是当前活动的标签页，切换到其他标签页
    if (activeResultTab.value === tabId) {
      // 如果还有标签页，切换到第一个
      if (updatedResultTabs.length > 0) {
        activeResultTab.value = updatedResultTabs[0].id;
      } else {
        // 没有标签页了，切换到主结果标签页
        activeResultTab.value = 'result';
      }
    }
  }
  
  console.log(`标签页关闭后, 剩余标签页数量: ${props.tab.resultTabs.length}`);
};

// 导出结果为CSV
const exportResultsToCSV = () => {
  try {
    if (!tab.results || !tab.results.length) {
      ElMessage.warning('没有可导出的数据');
      return;
    }
    
    // 获取列名
    const columns = tab.columns || Object.keys(tab.results[0]);
    
    // 创建CSV内容
    let csvContent = columns.join(',') + '\n';
    
    // 添加数据行
    tab.results.forEach(row => {
      const rowValues = columns.map(col => {
        const value = row[col];
        // 处理null值和包含逗号的值
        if (value === null) return '';
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      });
      csvContent += rowValues.join(',') + '\n';
    });
    
    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    
    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.href = url;
    link.setAttribute('download', `query_result_${new Date().toISOString().slice(0, 19).replace(/[:-]/g, '')}.csv`);
    link.style.visibility = 'hidden';
    
    // 触发下载
    document.body.appendChild(link);
    link.click();
    
    // 清理
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    ElMessage.success('导出成功');
  } catch (error) {
    console.error('导出CSV失败:', error);
    ElMessage.error('导出失败: ' + error.message);
  }
}



// 添加处理批量提交的函数
const handleSubmitChanges = async (changes) => {
  console.log('ActiveQueryPanel接收到批量提交请求:', changes);
  
  // 先检查是否为ClickHouse数据源，对于ClickHouse需要过滤掉update操作
  if (isClickhouseDataSource()) {
    // 如果有update操作，过滤掉并显示警告
    const hasUpdateChanges = changes.some(change => change.type === 'update');
    if (hasUpdateChanges) {
      const filteredChanges = changes.filter(change => change.type !== 'update');
      
      ElMessage.warning('ClickHouse数据源不支持修改操作，系统已自动过滤掉所有更新操作');
      
      // 如果过滤后没有任何操作，直接返回
      if (filteredChanges.length === 0) {
        ElMessage.info('所有变更都是修改操作，无法应用到ClickHouse数据源');
        return;
      }
      
      // 继续处理剩余的添加和删除操作
      changes = filteredChanges;
    }
  }
  
  if (changes.length > 0) {
    console.log('changes[0]的结构:', changes[0]);
    console.log('changes[0]的属性:', Object.keys(changes[0]));
  } else {
    ElMessage.warning('没有可提交的变更');
    return;
  }

  // 检查当前活动标签页的编辑模式状态
  let isEditModeEnabled = false;

  // 检查结果标签页的编辑模式
  if (activeResultTab.value.startsWith('result-tab-') || activeResultTab.value.startsWith('result-')) {
    // 通过ID在数组中查找实际索引
    const resultTabsArray = props.tab.resultTabs || [];
    const index = resultTabsArray.findIndex(tab => tab.id === activeResultTab.value);
    if (index !== -1) {
      isEditModeEnabled = resultTabsEditMode.value[index] || false;
    }
  }
  
  console.log('当前编辑模式状态:', { 
    activeTab: activeResultTab.value,
    isEditModeEnabled: isEditModeEnabled,
    resultTabsEditMode: resultTabsEditMode.value
  });

  if (!isEditModeEnabled) {
    console.log('当前处于只读模式，尝试自动切换到编辑模式');
    
    // 自动切换到编辑模式
    if (activeResultTab.value.startsWith('result-tab-') || activeResultTab.value.startsWith('result-')) {
      // 通过ID在数组中查找实际索引
      const resultTabsArray = props.tab.resultTabs || [];
      const index = resultTabsArray.findIndex(tab => tab.id === activeResultTab.value);
      if (index !== -1) {
        resultTabsEditMode.value[index] = true;
      }
    } else {
      // 默认为主标签页
      resultTabsEditMode.value['main'] = true;
      currentEditModeEnabled.value = true;

      // 通知父组件
      emit('update:tab', {
        id: props.tab.id,
        results: props.tab.results,
        columns: props.tab.columns,
        resultTabs: props.tab.resultTabs,
        executed: props.tab.executed,
        messages: props.tab.messages,
        error: props.tab.error,
        rowCount: props.tab.rowCount,
        editModeEnabled: true
      });
    }
    
    // 继续执行，不返回
    console.log('已自动切换到编辑模式，继续提交修改');
  }

  // 确定当前活动的结果标签，并从中提取表名
  let tableName = '';
  let currentSql = '';
  
  if (activeResultTab.value === 'result') {
    // 主结果标签
    tableName = props.tab.currentQueryTableName || extractTableName(props.tab.sql);
    currentSql = props.tab.sql;
  } else if (activeResultTab.value.startsWith('result-tab-')) {
    // 新格式的额外结果标签
    const tabNumber = parseInt(activeResultTab.value.split('-')[2]);
    const tabIndex = resultTabs.value.findIndex(tab => tab.id === activeResultTab.value);
    
    if (tabIndex !== -1) {
      tableName = resultTabs.value[tabIndex].currentQueryTableName || extractTableName(resultTabs.value[tabIndex].sql);
      currentSql = resultTabs.value[tabIndex].sql;
    }
  } else if (activeResultTab.value.startsWith('result-')) {
    // 旧格式的额外结果标签
    const index = parseInt(activeResultTab.value.split('-')[1]);
    if (resultTabs.value[index]) {
      tableName = resultTabs.value[index].currentQueryTableName || extractTableName(resultTabs.value[index].sql);
      currentSql = resultTabs.value[index].sql;
    }
  }
  
  if (!tableName) {
    // 尝试从当前SQL提取表名
    tableName = extractTableName(currentSql);
  }

  if (!tableName) {
    ElMessage.error('无法确定要操作的表名，请确保SQL中包含表名');
    return;
  }

  console.log('提取的表名:', tableName);
  
  // 更新标签页的表名信息
  if (activeResultTab.value === 'result' && props.tab) {
    props.tab.currentQueryTableName = tableName;
    console.log('更新后标签页信息:', props.tab);
  }   else if (activeResultTab.value.startsWith('result-tab-')) {
    // 新格式的额外结果标签
    const tabIndex = resultTabs.value.findIndex(tab => tab.id === activeResultTab.value);
    if (tabIndex !== -1) {
      resultTabs.value[tabIndex].currentQueryTableName = tableName;
      console.log(`更新后额外结果标签 ${tabIndex + 2} 信息:`, resultTabs.value[tabIndex]);
    }
  }
  else if (activeResultTab.value.startsWith('result-')) {
    // 旧格式的额外结果标签
    const index = parseInt(activeResultTab.value.split('-')[1]);
    if (resultTabs.value[index]) {
      resultTabs.value[index].currentQueryTableName = tableName;
      console.log(`更新后额外结果标签 ${index + 2} 信息:`, resultTabs.value[index]);
    }
  }

  // 检查changes是否有正确结构
  if (!changes || !Array.isArray(changes) || !changes.length) {
    console.error('无效的变更数据结构:', changes);
    ElMessage.error('无效的变更数据结构');
    return;
  }

  // 清理每个变更中的行数据，排除Vue内部属性
  const cleanedChanges = changes.map(change => {
    if (change.row) {
      const cleanRow = {};
      for (const key in change.row) {
        // 排除以__v开头的Vue内部属性和其他非数据属性
        if (!key.startsWith('__v') && key !== '_rowKey' && typeof change.row[key] !== 'function') {
          cleanRow[key] = change.row[key];
        }
      }
      return { ...change, row: cleanRow };
    }
    return change;
  });
  
  console.log('清理后的变更:', cleanedChanges);
  
  // 构建传递给父组件的数据结构
  const payload = {
    changes: cleanedChanges,
    tableName,
    datasourceId: selectedDatasourceId.value,
    database: selectedSchema.value,
    schema: selectedSchema.value,
    tabId: props.tab?.id,
    activeResultTab: activeResultTab.value // 添加当前活动的结果标签信息
  };

  console.log('向QueryTabsManager发送的payload:', payload);

  // 将数据传递给QueryTabsManager组件
  emit('submit:changes', payload);

  // 在本地数据中应用变更
  changes.forEach(change => {
    if (change.type === 'update') {
      let rows = props.tab.results;
      if (activeResultTab.value.startsWith('result-tab-')) {
        const idx = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
        if (props.tab.resultTabs && props.tab.resultTabs[idx]) {
          rows = props.tab.resultTabs[idx].results;
        }
      }
      if (rows && change.row && change.columnName) {
        // 使用更灵活的行匹配逻辑
        const rowIndex = rows.findIndex(r => {
          // 如果有 rowKey，优先使用 rowKey 匹配
          if (change.rowKey && r.__v_key) {
            return r.__v_key === change.rowKey;
          }
          // 如果有 id 字段，使用 id 匹配
          if (r.id !== undefined && change.row.id !== undefined) {
            return r.id === change.row.id;
          }
          // 否则使用所有字段匹配（除了正在修改的字段）
          return Object.keys(change.row).every(key => {
            if (key === change.columnName) return true; // 跳过正在修改的字段
            return r[key] === change.row[key];
          });
        });

        if (rowIndex !== -1) {
          rows[rowIndex][change.columnName] = change.newValue;
          console.log(`已在本地数据中应用修改: 行${rowIndex}, 字段${change.columnName}, 新值:`, change.newValue);
        } else {
          console.warn('未找到要修改的行:', change.row);
        }
      }
    }
    // 可扩展 add/delete 类型
  });

  // 提交成功后，清空表格组件中的待提交更改
  const tableRef = getCurrentTableRef();
  if (tableRef && tableRef.clearPendingChanges) {
    console.log('清空表格组件中的待提交更改');
    tableRef.clearPendingChanges();
  }
}

// 处理空结果时添加行
const handleEmptyAddRow = async () => {
  // 从SQL中提取表名
  const tableName = extractTableName();
  if (!tableName) {
    ElMessage.error('无法确定要操作的表名，请确保SQL中包含表名');
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: '加载表结构...',
    background: 'rgba(0, 0, 0, 0.7)'
  });

  try {
    // 获取表结构以确定字段
    const response = await getTableColumns(
      selectedDatasourceId.value, 
      tableName, 
      { schema: selectedSchema.value }
    );
    loading.close();

    // 如果没有找到表结构，使用一个空对象
    let newRow = {};
    
    if (response && Array.isArray(response)) {
      // 根据表结构创建一个空行
      response.forEach(column => {
        newRow[column.name || column.Field] = null;
      });

      // 弹出一个对话框，让用户填写关键字段
      ElMessageBox.confirm(
        '确认要在此表中添加一条新数据吗？',
        '添加确认',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        }
      ).then(async () => {
        const addLoading = ElLoading.service({
          lock: true,
          text: '正在添加...',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        
        try {
          // 调用新增API
          await addTableRow({
            datasourceId: selectedDatasourceId.value,
            database: selectedSchema.value,
            tableName: tableName,
            row: newRow
          });

          // 添加成功后重新执行查询
          await handleExecuteQuery();
          ElMessage.success('数据已添加');
        } catch (error) {
          console.error('添加失败:', error);
          ElMessage.error(error.response?.data?.message || '添加失败，请重试');
        } finally {
          addLoading.close();
        }
      }).catch(() => {
        // 用户取消添加
      });
    } else {
      ElMessage.error('获取表结构失败，无法添加数据');
    }
  } catch (error) {
    loading.close();
    console.error('获取表结构失败:', error);
    ElMessage.error(error.response?.data?.message || '获取表结构失败');
  }
};

// 提交单元格编辑
const submitCellEdit = async (row, column, newValue) => {
  // 如果值没有变化，则不提交更新
  if (row[column.property] === newValue) {
    ElMessage.info('值未发生变化，无需更新');
    return;
  }

  // 其他更新逻辑...
  // ...
};

// 处理对话框打开事件
const onDialogOpen = () => {
  // 检查是否是通过双击进入的编辑模式
  if (window._isDoubleClickEdit) {
    console.log('检测到双击编辑标记，强制设置为编辑模式');
    // 强制设置为编辑模式，确保在对话框打开时处于编辑状态
    isEditing.value = true;
    window._isDoubleClickEdit = false; // 清除标记
    console.log('对话框打开事件，当前编辑状态:', isEditing.value);
    
    // 使用setTimeout确保DOM已经更新
    setTimeout(() => {
      // 再次确认编辑状态
      isEditing.value = true;
      
      // 尝试聚焦到输入框
      const inputRef = document.querySelector('.cell-edit-content .el-input__inner');
      if (inputRef) {
        console.log('对话框打开后找到输入框元素，尝试聚焦');
        inputRef.focus();
      } else {
        console.log('对话框打开后未找到输入框元素');
      }
    }, 100);
  }
  // 如果处于编辑状态，尝试聚焦到输入框
  else if (isEditing.value) {
    setTimeout(() => {
      // 再次确认编辑状态
      isEditing.value = true;
      
      const inputRef = document.querySelector('.cell-edit-content .el-input__inner');
      if (inputRef) {
        console.log('对话框打开后找到输入框元素，尝试聚焦');
        inputRef.focus();
      } else {
        console.log('对话框打开后未找到输入框元素');
      }
    }, 100);
  }
  // 检查标题是否包含"编辑"，如果包含则强制设置为编辑模式
  else if (cellDetailTitle.value && cellDetailTitle.value.includes('编辑')) {
    console.log('检测到编辑标题，强制设置为编辑模式');
    isEditing.value = true;
    
    setTimeout(() => {
      const inputRef = document.querySelector('.cell-edit-content .el-input__inner');
      if (inputRef) {
        console.log('对话框打开后找到输入框元素，尝试聚焦');
        inputRef.focus();
      } else {
        console.log('对话框打开后未找到输入框元素');
      }
    }, 100);
  }
};

// 处理详情对话框关闭事件
const handleDetailDialogClosed = () => {
  console.log('对话框关闭事件，当前编辑状态:', isEditing.value);
  
  // 清理资源
  cellEditValue.value = '';
  
  // 不立即清除编辑上下文，以便下次可能重用
  // 但在延迟后清除，避免内存泄漏
  setTimeout(() => {
    editingCellContext.value = null;
  }, 1000);
}

// 关闭详情对话框
const closeDetailDialog = () => {
  // 如果正在编辑中，先提示
  if (isEditing.value) {
    ElMessageBox.confirm(
      '您有未保存的修改，确定要关闭吗？',
      '确认关闭',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 用户确认关闭
      isEditing.value = false; // 取消编辑状态
      cellDetailVisible.value = false;
    }).catch(() => {
      // 用户取消关闭，不执行任何操作
    });
  } else {
    // 不在编辑状态，直接关闭
    cellDetailVisible.value = false;
  }
}

// 已经存在 handleCellDblClick 函数

// 修改处理单元格详情提交的函数
const handleCellDetailSubmit = (data) => {
  console.log('收到单元格详情提交事件:', data);
  
  if (!isEditing.value) {
    console.warn('未处于编辑状态，忽略提交事件');
    return;
  }
  
  if (!editingCellContext.value) {
    // 如果没有编辑上下文，尝试从提交数据中构建
    if (data && data.rowData && data.columnName) {
      editingCellContext.value = {
        row: data.rowData,
        column: data.columnName,
        value: data.oldValue
      };
      console.log('从提交数据构建编辑上下文:', editingCellContext.value);
    } 
    // 尝试从当前标题和内容构建
    else if (cellDetailTitle.value && cellDetailContent.value !== undefined) {
      // 提取列名
      let columnName = cellDetailTitle.value;
      if (columnName.startsWith('编辑')) {
        columnName = columnName.replace('编辑', '').replace(':', '').trim();
      }
      
      // 使用当前tab中的当前行
      if (props.tab && props.tab.currentEditingRow && columnName) {
        editingCellContext.value = {
          row: props.tab.currentEditingRow,
          column: columnName,
          value: cellDetailContent.value
        };
        console.log('从当前编辑行构建编辑上下文:', editingCellContext.value);
      } else {
        ElMessage.error('编辑信息不完整，无法提交修改');
        return;
      }
    } else {
      ElMessage.error('编辑信息不完整，无法提交修改');
      return;
    }
  }
  
  // 更新编辑值
  if (data && data.newValue !== undefined) {
    cellEditValue.value = data.newValue;
  } else if (cellEditValue.value === '') {
    // 如果当前编辑值为空，使用原值
    cellEditValue.value = cellDetailContent.value;
  }
  
  console.log('准备提交编辑:', {
    editingContext: editingCellContext.value,
    newValue: cellEditValue.value
  });
  
  // 提交编辑
  submitEdit();
}

const isDML = (sql) => {
  if (!sql) return false;
  const lowerSql = sql.trim().toLowerCase();

  // 查询语句，不是DML
  if (lowerSql.startsWith('select ') ||
      lowerSql.startsWith('show ') ||
      lowerSql.startsWith('desc ') ||
      lowerSql.startsWith('describe ') ||
      lowerSql.startsWith('explain ')) {
    return false;
  }

  // 真正的DML语句（数据修改语句）
  return lowerSql.startsWith('delete ') ||
         lowerSql.startsWith('insert ') ||
         lowerSql.startsWith('update ') ||
         lowerSql.startsWith('replace ') ||
         lowerSql.startsWith('merge ');
};

const isDDL = (sql) => {
  if (!sql) return false;
  const lowerSql = sql.trim().toLowerCase();
  return lowerSql.startsWith('create ') ||
         lowerSql.startsWith('alter ') ||
         lowerSql.startsWith('drop ') ||
         lowerSql.startsWith('truncate ') ||
         lowerSql.startsWith('rename ');
};

const isSelect = (sql) => {
  if (!sql) return false;
  const lowerSql = sql.trim().toLowerCase();
  // 只有SELECT语句才被认为是查询，可以进行编辑
  // DESC、SHOW、EXPLAIN等只是描述性语句，不应该支持编辑
  return lowerSql.startsWith('select ') || lowerSql === 'select';
};

const backupSql = ref('');



const handleSaveChanges = async () => {
    // ... logic to get the new value and original value
    const { row, columnKey } = editingCellContext.value;
    const newValue = cellEditValue.value;
    const originalValue = row[columnKey];

    if (String(newValue) !== String(originalValue)) {
        // ... submit changes logic ...

        // Also update backupSql with the final value
        backupSql.value = generateUpdateSql(
          props.tab.currentQueryTableName || extractTableName(),
          { [columnKey]: originalValue },
          { [columnKey]: newValue }
        );

        // ... more logic to handle submission
    }
    cellDetailVisible.value = false;
};

// 处理生成备份SQL事件
  const handleGenerateBackupSql = (payload) => {
  console.log('=== 生成备份SQL调试信息 ===');
  console.log('收到生成备份SQL请求:', payload);
  console.log('变更数量:', payload?.changes?.length || 0);
  console.log('变更详情:', payload?.changes);
  console.log('当前活动标签页:', activeResultTab.value);

  if (!payload || !payload.changes || !payload.callback) {
    console.error('缺少必要的参数');
    return;
  }

  // 确保设置了数据库名称
  if (!payload.dbName) {
    console.warn('未提供数据库名称，使用当前选择的schema');
    payload.dbName = selectedSchema.value;
  }
  
  // 记录当前的显示SQL和提取的表名
  console.log('当前实际执行的SQL:', props.tab.displaySql);
  
  // 如果 displaySql 为空，尝试用当前激活 result 标签的 sql 字段兜底
  if (!props.tab.displaySql && activeResultTab.value && activeResultTab.value.startsWith('result-tab-')) {
    const idx = parseInt(activeResultTab.value.replace('result-tab-', '')) - 1;
    if (resultTabs.value[idx] && resultTabs.value[idx].sql) {
      props.tab.displaySql = resultTabs.value[idx].sql;
    }
  }
  // 如果 currentQueryTableName 为空，也尝试用当前 result 标签的 sql 提取
  if (!props.tab.currentQueryTableName && props.tab.displaySql) {
    const tableName = extractTableName(props.tab.displaySql);
    if (tableName) {
      props.tab.currentQueryTableName = tableName;
    }
  }
  
  // 确保从显示SQL（即实际执行的SQL）中提取表名
  let tableName = '';
  if (props.tab.displaySql) {
    tableName = extractTableName(props.tab.displaySql);
    console.log('从displaySql中提取的表名:', tableName);
    
    if (tableName) {
      // 立即更新currentQueryTableName，确保备份SQL生成使用正确的表名
      props.tab.currentQueryTableName = tableName;
      console.log('已更新标签页的表名为:', tableName);
    } else {
      console.warn('无法从displaySql中提取表名!');
    }
  }
  
  console.log('当前标签页表名信息:', props.tab.currentQueryTableName);
  
  // 记录变更信息，帮助调试
  const updateChanges = payload.changes.filter(c => c.type === 'update');
  if (updateChanges.length > 0) {
    updateChanges.forEach(change => {
      console.log('UPDATE变更详情:', {
        字段: change.columnName,
        新值: change.newValue,
        原值: change.originalValue
      });
      
      // 确保变更中提供了原始值，这对于正确生成WHERE条件很重要
      if (change.originalValue === undefined && change.row) {
        change.originalValue = change.row[change.columnName];
        console.log(`补充原始值: ${change.columnName} = ${change.originalValue}`);
      }
    });
  }
  
  // 生成备份SQL
  console.log('开始生成备份SQL...');
  const backupSql = generateBatchBackupSql(payload.changes);
  console.log('生成的备份SQL:', backupSql);

  // 生成执行SQL
  console.log('开始生成执行SQL...');
  const executionSql = generateExecutionSql(payload.changes);
  console.log('生成的执行SQL:', executionSql);

  // 注意：不在这里显示备份弹窗，因为手动编辑结果提交有自己的确认弹窗
  // 备份弹窗只在编辑器执行SQL时显示（通过 prepareBackupAndConfirm 函数）

  // 调用回调函数返回备份SQL和执行SQL（即使为空也传递占位字符串，保证弹窗）
  console.log('调用回调函数，传递SQL结果');
  payload.callback(backupSql || '--无备份SQL--', executionSql || '--无执行SQL--');
};

// 生成批量备份SQL
const generateBatchBackupSql = (changes) => {
  if (!changes || !Array.isArray(changes) || changes.length === 0) {
    return '';
  }
  
  console.log('生成批量备份SQL:', changes);
  
  // 从当前激活的tab获取正确的表名
  let tableName = '';
  if (activeResultTab.value === 'result') {
    tableName = props.tab.currentQueryTableName || 
                (props.tab.displaySql ? extractTableName(props.tab.displaySql) : null) || 
                extractTableName();
    // 新增兜底：如果还没有，尝试用 resultTabs
    if (!tableName && resultTabs.value.length > 0 && resultTabs.value[0].sql) {
      tableName = extractTableName(resultTabs.value[0].sql);
    }
  } else if (activeResultTab.value.startsWith('result-tab-')) {
    // 通过标签页ID查找对应的标签页
    const targetTab = resultTabs.value.find(tab => tab.id === activeResultTab.value);
    if (targetTab) {
      console.log('找到目标标签页:', targetTab);
      tableName = targetTab.currentQueryTableName ||
                 (targetTab.sql ? extractTableName(targetTab.sql) : null) ||
                 extractTableName();
      // 新增兜底
      if (!tableName && targetTab.sql) {
        tableName = extractTableName(targetTab.sql);
      }
      console.log('从目标标签页提取的表名:', tableName);
    } else {
      console.warn('未找到对应的标签页:', activeResultTab.value);
      console.warn('可用的标签页:', resultTabs.value.map(tab => ({ id: tab.id, sql: tab.sql })));
    }
  }
  console.log('生成批量备份SQL - 当前表名:', tableName);
  console.log('生成批量备份SQL - 当前活动标签页:', activeResultTab.value);
  console.log('生成批量备份SQL - props.tab.currentQueryTableName:', props.tab.currentQueryTableName);
  console.log('生成批量备份SQL - selectedSchema:', selectedSchema.value);

  if (!tableName) {
    console.warn('无法确定表名，无法生成备份SQL');
    console.warn('调试信息 - activeResultTab:', activeResultTab.value);
    console.warn('调试信息 - resultTabs:', resultTabs.value);
    console.warn('调试信息 - props.tab:', props.tab);
    return '';
  }
  
  // 根据变更类型生成不同的备份SQL
  const backupSqlParts = [];
  
  // 收集所有受影响的行数据，用于生成INSERT备份语句
  const rowsToBackup = [];
  
  changes.forEach((change) => {
    if (change.type === 'update' || change.type === 'delete') {
      // 对于更新和删除操作，我们需要备份原始行数据
      if (change.row && typeof change.row === 'object') {
        // 创建一个不包含Vue内部属性的行数据副本
        const cleanRow = {};
        for (const key in change.row) {
          if (!key.startsWith('__v') && !key.startsWith('_')) {
            if (change.type === 'update' && key === change.columnName) {
              // 对于更新操作，使用原始值
              cleanRow[key] = change.originalValue;
            } else {
              cleanRow[key] = change.row[key];
            }
          }
        }
        rowsToBackup.push(cleanRow);
      }
    }
  });
  
  // 生成INSERT备份语句
  if (rowsToBackup.length > 0) {
    // 获取完整的限定表名，包括数据库名
    let cleanTableName = tableName.replace(/`/g, '');
    let qualifiedTableName = cleanTableName;
    if (selectedSchema.value) {
      qualifiedTableName = `\`${selectedSchema.value}\`.\`${cleanTableName}\``;
      console.log(`使用完整的限定表名: ${qualifiedTableName}`);
    }
  
    const backupSql = generateInsertSql(
        qualifiedTableName,
        rowsToBackup
      );
      backupSqlParts.push(backupSql);
    }
  
    return backupSqlParts.join('\n\n');
  };

  // 生成执行SQL
  const generateExecutionSql = (changes) => {
    if (!changes || !Array.isArray(changes) || changes.length === 0) {
      return '';
    }
    
    console.log('生成执行SQL:', changes);
    
    // 从当前激活的tab获取正确的表名
    let tableName = '';
    if (activeResultTab.value === 'result') {
      tableName = props.tab.currentQueryTableName || 
                  (props.tab.displaySql ? extractTableName(props.tab.displaySql) : null) || 
                  extractTableName();
      // 新增兜底：如果还没有，尝试用 resultTabs
      if (!tableName && resultTabs.value.length > 0 && resultTabs.value[0].sql) {
        tableName = extractTableName(resultTabs.value[0].sql);
      }
    } else if (activeResultTab.value.startsWith('result-tab-')) {
      // 通过标签页ID查找对应的标签页
      const targetTab = resultTabs.value.find(tab => tab.id === activeResultTab.value);
      if (targetTab) {
        console.log('找到目标标签页:', targetTab);
        tableName = targetTab.currentQueryTableName ||
                   (targetTab.sql ? extractTableName(targetTab.sql) : null) ||
                   extractTableName();
        // 新增兜底
        if (!tableName && targetTab.sql) {
          tableName = extractTableName(targetTab.sql);
        }
        console.log('从目标标签页提取的表名:', tableName);
      } else {
        console.warn('未找到对应的标签页:', activeResultTab.value);
        console.warn('可用的标签页:', resultTabs.value.map(tab => ({ id: tab.id, sql: tab.sql })));
      }
    }
    console.log('生成执行SQL - 当前表名:', tableName);
    console.log('生成执行SQL - 当前活动标签页:', activeResultTab.value);
    console.log('生成执行SQL - props.tab.currentQueryTableName:', props.tab.currentQueryTableName);
    console.log('生成执行SQL - selectedSchema:', selectedSchema.value);

    if (!tableName) {
      console.warn('无法确定表名，无法生成执行SQL');
      console.warn('调试信息 - activeResultTab:', activeResultTab.value);
      console.warn('调试信息 - resultTabs:', resultTabs.value);
      console.warn('调试信息 - props.tab:', props.tab);
      return '';
    }
    
    // 获取完整的限定表名，包括数据库名
    let cleanTableName = tableName.replace(/`/g, '');
    let qualifiedTableName = cleanTableName;
    if (selectedSchema.value) {
      qualifiedTableName = `\`${selectedSchema.value}\`.\`${cleanTableName}\``;
      console.log(`使用完整的限定表名: ${qualifiedTableName}`);
    }
    
    const { generateUpdateSql, generateDeleteSql, generateInsertSql } = useBackupSqlGenerator();
    
    // 根据变更类型生成不同的执行SQL
    const executionSqlParts = [];
    
    // 处理每个变更
    changes.forEach((change) => {
           if (change.type === 'update') {
         // 生成UPDATE语句
         const whereConditions = {};
         
         // 特殊处理可能的大整数ID字段
         if (change.row) {
           // 检查是否有comment_id或其他大整数ID字段
           for (const key in change.row) {
             if (/^(comment_id|id|.*_id)$/i.test(key) && 
                 ((typeof change.row[key] === 'number' && Math.abs(change.row[key]) > 1000000000000) ||
                 (typeof change.row[key] === 'string' && /^\d{15,}$/.test(change.row[key])))) {
               console.log(`检测到大整数ID字段: ${key}=${change.row[key]}`);
               // 在whereConditions中明确设置，确保它被正确处理
               // 传递字段名，以便formatSqlValue可以特殊处理comment_id
               whereConditions[key] = change.row[key];
             }
           }
         }
         
         // 设置要更新的字段值
         const setValues = { [change.columnName]: change.newValue };
         console.log(`UPDATE操作: 更新字段=${change.columnName}, 新值=${change.newValue}`);
         
         // 生成SQL
         const updateSql = generateUpdateSql(qualifiedTableName, whereConditions, setValues, change.row);
         if (updateSql) {
           executionSqlParts.push(updateSql);
         }
      } else if (change.type === 'delete') {
        // 生成DELETE语句
        const whereConditions = {};
        // 从row中提取主键或唯一标识列，这里我们使用所有非空字段
        for (const key in change.row) {
          if (!key.startsWith('__v') && !key.startsWith('_') && 
              change.row[key] !== undefined && change.row[key] !== null) {
            whereConditions[key] = change.row[key];
          }
        }
        const deleteSql = generateDeleteSql(qualifiedTableName, whereConditions);
        if (deleteSql) {
          executionSqlParts.push(deleteSql);
        }
      } else if (change.type === 'add') {
        // 生成INSERT语句 - 清理行数据，移除内部字段
        const cleanRow = {};
        for (const key in change.row) {
          if (!key.startsWith('__v') && !key.startsWith('_')) {
            cleanRow[key] = change.row[key];
          }
        }
        console.log('清理后的新增行数据:', cleanRow);
        const insertSql = generateInsertSql(qualifiedTableName, [cleanRow]);
        if (insertSql) {
          executionSqlParts.push(insertSql);
        }
      }
    });
    
    return executionSqlParts.join('\n\n');
  };

  // 处理取消操作
  const handleCancelQuery = () => {
    ElMessageBox.confirm(
      killQueryInDatabase.value
        ? '确定要取消当前操作并终止数据库中的进程吗？'
        : '确定要取消当前操作吗？',
      '取消操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    ).then(() => {
      // 添加取消操作的日志
      addExecutionLog(`用户取消了操作${killQueryInDatabase.value ? '，并终止了数据库中的操作进程' : ''}`, 'warning');
      
      // 重置执行状态
      resetExecutingState();
      
      // 通知父组件取消操作
      emit('cancel-query', {
        tabId: props.tab.id,
        killInDatabase: killQueryInDatabase.value,
        datasourceId: selectedDatasourceId.value,
        schema: selectedSchema.value,
        sqlContent: sqlContent.value // 传递SQL内容，用于生成查询哈希值
      });
      
      // 显示取消成功消息
      ElMessage({
        message: killQueryInDatabase.value
          ? '已取消操作并尝试终止数据库中的操作进程'
          : '已取消操作',
        type: 'success'
      });
    }).catch(() => {
      // 用户取消操作，不做任何处理
    });
  };

  // 这段代码已经移到组件开头，此处保留注释以便于理解代码结构

  // 确保在组件挂载时初始化结果表引用数组
  onMounted(() => {
    try {
      // 预先分配足够的空间，避免动态扩展时的undefined错误
      if (resultTabs.value && resultTabs.value.length > 0) {
        resultTableRefs.value = new Array(resultTabs.value.length).fill(undefined);
        console.log(`初始化resultTableRefs数组，长度: ${resultTableRefs.value.length}`);
      } else {
        // 如果resultTabs.value不存在或为空，至少初始化一个空数组
        resultTableRefs.value = [];
        console.log('初始化空的resultTableRefs数组');
      }
      
      // 延迟一下，确保表格组件已经渲染完成
      setTimeout(() => {
        // 尝试获取当前活动标签页的表格引用
        const currentTableRef = getCurrentTableRef();
        if (currentTableRef) {
          console.log('组件挂载后成功获取表格引用');
          // 如果主表格引用为空，则使用当前获取到的引用
          if (!editableResultTableRef.value) {
            editableResultTableRef.value = currentTableRef;
            console.log('已设置editableResultTableRef为当前表格引用');
          }
        } else {
          console.warn('组件挂载后无法获取表格引用');
        }
      }, 200);
    } catch (error) {
      console.error('初始化resultTableRefs数组时出错:', error);
      // 确保至少有一个空数组
      resultTableRefs.value = [];
    }
  });

  // 监听resultTabs变化，确保resultTableRefs数组长度同步
  watch(() => resultTabs.value && resultTabs.value.length, (newLength, oldLength) => {
    // 如果resultTabs.value为undefined或null，直接返回
    if (!resultTabs.value) {
      console.log('resultTabs.value为undefined或null，跳过同步操作');
      return;
    }
    
    console.log(`resultTabs数组长度从 ${oldLength} 变更为 ${newLength}`);
    
    // 确保resultTableRefs数组长度与resultTabs同步
    if (newLength > resultTableRefs.value.length) {
      // 数组扩展
      const additionalItems = new Array(newLength - resultTableRefs.value.length).fill(undefined);
      resultTableRefs.value = [...resultTableRefs.value, ...additionalItems];
      console.log(`扩展resultTableRefs数组，新长度: ${resultTableRefs.value.length}`);
    }
  });

  // 添加一个标志位，用于防止在编辑模式状态变更时错误地切换标签页
  const isUpdatingEditMode = ref(false);

  // 监听activeResultTab的变化
  watch(activeResultTab, (newTab, oldTab) => {
    // 标签页切换时重置选择状态
    selectedRowsCount.value = 0;

    // 如果是由编辑模式状态变更触发的，则不进行额外处理
    if (isUpdatingEditMode.value) {
      return;
    }
    
    // 标签页切换时保持编辑模式状态一致
    nextTick(() => {
      if (newTab.startsWith('result-tab-') || newTab.startsWith('result-')) {
        // 切换到额外结果标签页，通过ID查找实际索引
        const resultTabsArray = props.tab.resultTabs || [];
        const index = resultTabsArray.findIndex(tab => tab.id === newTab);

        if (index !== -1) {
          // 更新editableResultTableRef引用，指向当前活动的表格
          if (resultTableRefs.value && Array.isArray(resultTableRefs.value) &&
              index >= 0 && index < resultTableRefs.value.length && resultTableRefs.value[index]) {
            editableResultTableRef.value = resultTableRefs.value[index];
            console.log(`已更新editableResultTableRef引用为当前活动标签页 ${newTab} 的表格组件`);
          } else {
            console.warn(`无法找到标签页 ${newTab} 的表格引用`);
          }

          if (resultTableRefs.value && resultTableRefs.value[index]) {
            console.log(`切换到额外结果标签 ${index + 1}，设置编辑模式为: ${resultTabsEditMode.value[index] || false}`);

            // 强制刷新当前标签页的表格
            if (resultTableRefs.value[index] && resultTableRefs.value[index].updateTableData) {
              try {
                const targetTab = resultTabsArray[index];
                if (targetTab && targetTab.results) {
                  console.log(`强制刷新额外结果标签 ${index + 1} 的表格数据`);
                  // 使用分页数据而不是全部数据
                  const paginatedData = getResultTabPaginatedData(targetTab);
                  resultTableRefs.value[index].updateTableData(paginatedData);
                } else {
                  console.log(`额外结果标签 ${index + 1} 没有数据或结果标签不存在`);
                }
              } catch (err) {
                console.error(`刷新额外结果标签 ${index + 1} 时出错:`, err);
              }
            }
          }
        }
      } else if (newTab === 'result') {
        // 切换到主结果标签页
        if (editableResultTableRef.value) {
          console.log(`切换到主结果标签，设置编辑模式为: ${resultTabsEditMode.value['main'] || false}`);
          
          // 强制刷新主结果表格
          if (editableResultTableRef.value && editableResultTableRef.value.updateTableData) {
            try {
              if (props.tab && props.tab.results && props.tab.results.length > 0) {
                console.log('强制刷新主结果表格数据');
                // 使用分页数据而不是全部数据
                const start = (currentPage.value - 1) * pageSize.value;
                const end = start + pageSize.value;
                const paginatedData = props.tab.results.slice(start, end);
                editableResultTableRef.value.updateTableData(paginatedData);
              } else {
                console.log('主结果表没有数据或标签页不存在');
              }
            } catch (err) {
              console.error('刷新主结果表时出错:', err);
            }
          }
        }
      }
    });
  });

  // updateTableData方法已合并到上面的defineExpose中

  // ... existing code ...
  // 监听执行状态变化
  watch(() => props.tab.isExecuting, (newVal, oldVal) => {
    console.log(`[ActiveQueryPanel] 执行状态变化: ${oldVal} -> ${newVal}`);
    
    // 如果查询从执行中变为完成状态
    if (oldVal === true && newVal === false) {
      console.log('[ActiveQueryPanel] 查询执行完成');
    }
  });

  // 处理可编辑表格组件的编辑模式切换
  const handleToggleEditMode = (isReadOnly, tabIndex = 'main') => {
    console.log(`表格组件请求切换编辑模式: isReadOnly=${isReadOnly}, tabIndex=${tabIndex}`);

    // 设置标志位，防止在编辑模式状态变更时错误地切换标签页
    isUpdatingEditMode.value = true;

    // 检查用户是否有编辑权限
    if (!isReadOnly && !canEnableEditMode.value) {
      console.log('用户没有编辑权限，无法切换到编辑模式');
      ElMessage.warning('您没有编辑权限，请联系管理员');
      isUpdatingEditMode.value = false; // 重置标志位
      return;
    }

    // 更新对应标签页的编辑模式
    resultTabsEditMode.value[tabIndex] = !isReadOnly;
    console.log(`已更新标签页 ${tabIndex} 的编辑模式为: ${!isReadOnly ? '可编辑' : '只读'}`);

    // 切换到编辑模式时，重置分页到第一页
    if (!isReadOnly) {
      if (tabIndex === 'main') {
        // 主结果标签页
        currentPage.value = 1;
        console.log('切换到编辑模式，重置主结果标签页分页到第一页');
      } else if (typeof tabIndex === 'number') {
        // 额外结果标签页（数字索引）
        const resultTab = props.tab.resultTabs?.[tabIndex];
        if (resultTab) {
          resultTab.currentPage = 1;
          console.log(`切换到编辑模式，重置结果标签页 ${resultTab.id} 分页到第一页`);
        }
      } else {
        // 额外结果标签页（字符串索引）
        const resultTab = props.tab.resultTabs?.[tabIndex];
        if (resultTab) {
          resultTab.currentPage = 1;
          console.log(`切换到编辑模式，重置结果标签页 ${resultTab.id} 分页到第一页`);
        }
      }
    }

    // 如果是主结果标签页，同时更新全局编辑模式开关状态
    if (tabIndex === 'main') {
      currentEditModeEnabled.value = !isReadOnly;

      // 发出事件通知父组件编辑模式状态变更
      emit('update:tab', {
        id: props.tab.id,
        results: props.tab.results,
        columns: props.tab.columns,
        resultTabs: props.tab.resultTabs,
        executed: props.tab.executed,
        messages: props.tab.messages,
        error: props.tab.error,
        rowCount: props.tab.rowCount,
        editModeEnabled: !isReadOnly
      });

      console.log(`已通知父组件更新编辑模式状态为: ${!isReadOnly ? '可编辑' : '只读'}`);
    }
    
    // 获取当前表格引用并强制更新其isReadOnly属性
    nextTick(() => {
      let tableRef;
      if (tabIndex === 'main') {
        tableRef = getCurrentTableRef();
      } else if (typeof tabIndex === 'number') {
        // 数字索引，直接使用
        tableRef = resultTableRefs.value[tabIndex];
      } else {
        // 字符串索引，可能需要转换
        tableRef = resultTableRefs.value[tabIndex];
      }

      if (tableRef && tableRef.isReadOnly !== undefined) {
        tableRef.isReadOnly = isReadOnly;
        console.log(`已强制更新表格组件的isReadOnly属性为: ${isReadOnly}`);

        // 如果是切换到编辑模式，才重新加载数据以反映分页重置
        // 如果是切换到只读模式，保持当前表格数据不变（保留用户的修改）
        if (!isReadOnly && tableRef.updateTableData) {
          if (tabIndex === 'main') {
            // 主结果标签页：使用主结果数据的分页
            const mainResults = props.tab.results || [];
            const start = (currentPage.value - 1) * pageSize.value;
            const end = start + pageSize.value;
            const paginatedData = mainResults.slice(start, end);
            tableRef.updateTableData(paginatedData);
            console.log('已更新主结果标签页表格数据，重置到第一页');
          } else {
            // 额外结果标签页：使用标签页自己的分页数据
            const resultTabIndex = typeof tabIndex === 'number' ? tabIndex : parseInt(tabIndex);
            const resultTab = props.tab.resultTabs?.[resultTabIndex];
            if (resultTab) {
              const paginatedData = getResultTabPaginatedData(resultTab);
              tableRef.updateTableData(paginatedData);
              console.log(`已更新结果标签页 ${resultTab.id} 表格数据，重置到第一页`);
            }
          }
        } else if (!isReadOnly && tableRef.tableData && Array.isArray(tableRef.tableData.value)) {
          tableRef.tableData.value = [...tableRef.tableData.value];
          console.log('已强制刷新表格视图');
        } else if (isReadOnly) {
          console.log('切换到只读模式，保持当前表格数据不变（保留用户修改）');
        }
      }
      
      // 重置标志位
      setTimeout(() => {
        isUpdatingEditMode.value = false;
        console.log('编辑模式切换完成，重置标志位');
      }, 100);
    });
  };

  // 在 <script setup> 末尾添加
  function checkAndBackupBeforeExecute(sql, tableName) {
    // 直接调用 prepareBackupAndConfirm，如果有返回Promise则返回
    if (typeof prepareBackupAndConfirm === 'function') {
      return prepareBackupAndConfirm(sql, tableName);
    }
    // 兜底：直接返回已完成Promise
    return Promise.resolve();
  }

  // 独立分页组件相关的计算属性和方法

  // 检查是否有活动的结果数据
  const hasActiveResultData = computed(() => {
    if (!props.tab.executed) return false;

    // 如果是主结果标签页
    if (activeResultTab.value === 'result') {
      return props.tab.results && props.tab.results.length > 0;
    }

    // 如果是额外结果标签页
    const resultTabsArray = props.tab.resultTabs || [];
    const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
    return activeTab && activeTab.results && activeTab.results.length > 0;
  });

  // 当前活动标签页的页码
  const currentActiveTabPage = computed(() => {
    if (activeResultTab.value === 'result') {
      return currentPage.value;
    }

    const resultTabsArray = props.tab.resultTabs || [];
    const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
    return activeTab ? (activeTab.currentPage || 1) : 1;
  });

  // 当前活动标签页的页大小
  const currentActiveTabPageSize = computed(() => {
    if (activeResultTab.value === 'result') {
      return pageSize.value;
    }

    const resultTabsArray = props.tab.resultTabs || [];
    const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
    return activeTab ? (activeTab.pageSize || 20) : 20;
  });

  // 当前活动标签页的总数据量
  const currentActiveTabTotal = computed(() => {
    if (activeResultTab.value === 'result') {
      return props.tab.results ? props.tab.results.length : 0;
    }

    const resultTabsArray = props.tab.resultTabs || [];
    const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
    return activeTab && activeTab.results ? activeTab.results.length : 0;
  });

  // 独立分页组件的页大小变化处理
  const handleIndependentSizeChange = (size) => {
    if (activeResultTab.value === 'result') {
      handleSizeChange(size);
    } else {
      const resultTabsArray = props.tab.resultTabs || [];
      const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
      if (activeTab) {
        handleResultTabSizeChange(activeTab, size);
      }
    }
  };

  // 独立分页组件的页码变化处理
  const handleIndependentPageChange = (page) => {
    if (activeResultTab.value === 'result') {
      handleCurrentChange(page);
    } else {
      const resultTabsArray = props.tab.resultTabs || [];
      const activeTab = resultTabsArray.find(tab => tab.id === activeResultTab.value);
      if (activeTab) {
        handleResultTabPageChange(activeTab, page);
      }
    }
  };

// 格式化错误消息，使其更友好
const formatErrorMessage = (errorMsg) => {
  if (!errorMsg) return '未知错误';

  // 处理MySQL错误元组格式 (error_code, error_message)
  if (typeof errorMsg === 'string' && errorMsg.startsWith('(') && errorMsg.includes(',')) {
    try {
      // 尝试解析元组格式的错误信息
      const match = errorMsg.match(/^\((\d+),\s*["'](.+)["']\)$/);
      if (match) {
        const errorCode = match[1];
        const errorMessage = match[2];

        // 根据错误代码提供更友好的提示
        switch (errorCode) {
          case '1064':
            return `SQL语法错误: ${errorMessage}`;
          case '1146':
            return `表不存在: ${errorMessage}`;
          case '1054':
            return `字段不存在: ${errorMessage}`;
          case '1045':
            return `访问被拒绝: ${errorMessage}`;
          case '1049':
            return `数据库不存在: ${errorMessage}`;
          case '2006':
            return `数据库连接已断开: ${errorMessage}`;
          case '2013':
            return `连接丢失: ${errorMessage}`;
          default:
            return `数据库错误(${errorCode}): ${errorMessage}`;
        }
      }
    } catch (e) {
      // 如果解析失败，继续使用原始错误信息
    }
  }

  // 处理常见的错误提示，使其更友好
  if (errorMsg.includes('permission denied') || errorMsg.includes('没有权限')) {
    return '您暂时没有执行此查询的权限，请联系系统管理员申请';
  }

  if (errorMsg.includes('syntax error') || errorMsg.includes('语法错误')) {
    return '查询语法错误，请检查SQL语句';
  }

  if (errorMsg.includes('table') && (errorMsg.includes('not exist') || errorMsg.includes('不存在'))) {
    return '表不存在，请确认表名是否正确';
  }

  if (errorMsg.includes('column') && (errorMsg.includes('not exist') || errorMsg.includes('不存在'))) {
    return '字段不存在，请确认字段名是否正确';
  }

  // 如果没有匹配到常见错误，返回原始错误
  return errorMsg;
};

</script>
defineExpose({
  // ...已有暴露内容...
  checkAndBackupBeforeExecute,
  // ...已有暴露内容...
});
<style scoped>
.query-panel {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  overflow: hidden; /* 改为hidden，让子元素自己处理滚动 */
  position: relative;
}

.editor-container {
  flex: 0 0 auto;
  min-height: 150px;
  max-height: 40vh; /* 限制编辑器最大高度 */
  border-bottom: 1px solid #dcdfe6;
  padding: 0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.codemirror-container {
  height: 100%;
  width: 100%;
  overflow: hidden;
}

/* 确保CodeMirror编辑器内部滚动正常工作 */
:deep(.cm-editor) {
  height: 100%;
  width: 100%;
}

:deep(.cm-scroller) {
  overflow: auto;
  font-family: Consolas, Monaco, 'Andale Mono', 'Ubuntu Mono', monospace;
}

/* DMS风格顶部工具栏样式 */
.top-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 8px;
  background: linear-gradient(135deg, #5883d2 0%, #5883d2 100%);
  border-bottom: 1px solid #9dbbd7;
  gap: 12px;
  min-height: 44px;
  flex-shrink: 0; /* 防止被压缩 */
  position: sticky;
  top: 0;
  z-index: 10; /* 确保在其他元素之上 */
}
.edit-actions {
  display: flex;
  align-items: center;
  gap: 6px;
}

.edit-actions .el-button {
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
  color: white;
  font-weight: 500;
  padding: 6px 12px;
  font-size: 12px;
  height: 28px;
}

.edit-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.5);
}

.edit-actions .el-button--primary {
  background: rgba(255, 255, 255, 0.9);
  color: #1890ff;
  border-color: transparent;
}

.edit-actions .el-button--primary:hover {
  background: white;
  color: #1890ff;
}

.edit-actions .el-button .el-icon {
  margin-right: 4px;
}

.datasource-selectors {
  display: flex;
  align-items: center;
  gap: 12px;
}

.datasource-selectors .el-select {
  min-width: 140px;
}

.datasource-selectors .el-select .el-input__wrapper {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

/* DMS风格结果标签页导航样式 */
.result-tabs-nav {
  display: flex;
  align-items: center;
  padding: 0 12px;
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  gap: 1px;
  min-height: 32px;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  cursor: pointer;
  transition: all 0.2s ease;
  gap: 4px;
  font-size: 12px;
  color: #666666;
  margin-right: 2px;
  position: relative;
  min-width: auto;
  white-space: nowrap;
}

.tab-item:hover {
  background-color: #e6f7ff;
  color: #1890ff;
  border-color: #91d5ff;
}

.tab-item.active {
  background-color: #ffffff;
  color: #1890ff;
  font-weight: 500;
  border-color: #1890ff;
  border-bottom: 1px solid #ffffff;
  z-index: 1;
}

.tab-item .el-icon {
  color: #6daeea;
}

.close-icon {
  margin-left: 4px;
  padding: 1px 3px;
  border-radius: 2px;
  font-size: 10px;
  line-height: 1;
  opacity: 0.6;
  transition: all 0.2s ease;
  width: 14px;
  height: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-icon:hover {
  background-color: #ff4d4f;
  color: white;
  opacity: 1;
  transform: scale(1.1);
}

/* DMS风格结果内容区域样式 */
.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  min-height: 350px; /* 设置最小高度确保内容区域有足够空间 */
  overflow: visible; /* 允许分页组件显示 */
  height: 100%; /* 占满父容器高度 */
  padding-bottom: 0; /* 移除底部内边距 */
}

.result-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  border-top: none;
  min-height: 300px; /* 设置最小高度确保表格面板有足够空间 */
  overflow: visible; /* 允许分页组件显示 */
  height: 100%; /* 占满父容器高度 */
  position: relative; /* 为分页组件提供定位参考 */
}

.result-stats {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 12px;
  background: linear-gradient(90deg, #f0f9ff 0%, #e6f7ff 100%);
  border-bottom: 1px solid #d9d9d9;
  font-size: 12px;
  color: #1890ff;
  font-weight: 500;
  min-height: 28px;
}

.record-count {
  display: flex;
  align-items: center;
  gap: 6px;
}

.record-count .el-icon {
  color: #1890ff;
  font-size: 14px;
}

/* 原有的结果内分页组件样式（已废弃，使用独立分页组件） */
.result-pagination {
  display: none; /* 隐藏原有的分页组件 */
}



/* DMS风格分页组件样式 */
.result-pagination .el-pagination {
  --el-pagination-font-size: 13px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #666666;
  --el-pagination-border-radius: 4px;
}



.result-pagination .el-pagination .el-pager li {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  margin: 0 2px;
  border-radius: 4px;
}

.result-pagination .el-pagination .el-pager li:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.result-pagination .el-pagination .el-pager li.is-active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

/* DMS风格结果容器 */
.result-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  overflow: visible;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  margin: 8px 16px 100px 16px; /* 增加底部空间为分页组件留位置 */
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  max-height: calc(100vh - 520px); /* 限制最大高度 */
}

/* 添加空结果容器样式 */
.empty-result-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa;
  padding: 20px;
  border-radius: 4px;
  margin: 10px;
  border: 1px dashed #dcdfe6;
  
  .result-message {
    margin-top: 10px;
    color: #409eff;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
  }
}

/* DMS风格结果容器 */
.results-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  margin: 8px 16px 16px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  min-height: 400px; /* 设置最小高度确保表格容器有足够空间 */
  max-height: calc(100vh - 200px); /* 增加可用高度 */
  overflow: visible; /* 允许分页组件显示 */
  position: relative;
}

/* 旧的标签样式已移除，使用新的 .result-tabs-nav 和 .tab-item 样式 */

/* DMS风格表格样式 */
.result-panel .editable-result-table {
  border: none;
  flex: 1;
  overflow: hidden; /* 让表格组件自己处理滚动 */
  min-height: 0; /* 允许flex收缩 */
}

.result-panel .el-table {
  border: none;
  font-size: 13px;
}

.result-panel .el-table th {
  background-color: #fafafa;
  border-bottom: 1px solid #d9d9d9;
  color: #262626;
  font-weight: 600;
  padding: 12px 8px;
}

.result-panel .el-table td {
  border-bottom: 1px solid #f0f0f0;
  padding: 8px;
  color: #595959;
}

.result-panel .el-table tr:hover td {
  background-color: #e6f7ff;
}

.result-panel .el-table .el-table__row--striped td {
  background-color: #fafafa;
}

.result-panel .el-table .el-table__row--striped:hover td {
  background-color: #e6f7ff;
}

/* DMS风格无数据和错误提示 */
.no-data-message {
  padding: 40px;
  text-align: center;
  background-color: #fafafa;
  border-top: 1px solid #d9d9d9;
}

.execution-message {
  padding: 16px;
  background-color: #f6ffed;
  border-top: 1px solid #b7eb8f;
  margin-bottom: 16px;
}

.execution-message .el-alert {
  background-color: transparent;
  border: none;
}

.error-message {
  padding: 16px;
  background-color: #fff2f0;
  border-top: 1px solid #ffccc7;
}

.error-message .el-alert {
  background-color: transparent;
  border: none;
}

/* DMS风格整体容器 */
.active-query-panel {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 新的左右分栏布局样式 */
.multi-sql-execution-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  position: relative;
  min-height: 200px;
  /* 不设置height/max-height，交给内部绝对定位撑开 */
}
.multi-sql-execution-layout .sql-results-panel {
  flex: 1 1 0%;
  position: relative;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 0 8px 8px 0;
  overflow: hidden;
}
.multi-sql-execution-layout .sql-results-list {
  position: absolute;
  top: 0; left: 0; right: 0; bottom: 0;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  padding: 16px 16px 80px 16px;
  box-sizing: border-box;
  background: #fff;
}

/* 左侧执行概览面板 */
.execution-summary-panel {
  flex: 0 0 300px; /* 固定宽度300px */
  min-width: 280px;
}

.execution-summary {
  background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  height: 100%;
}

.summary-header {
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-title {
  font-size: 14px;
  font-weight: 600;
  color: #1890ff;
}

.summary-tag {
  margin-left: auto;
}

.summary-stats {
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-size: 14px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  border-left: 3px solid #e9ecef;
}

.stat-item.success {
  color: #52c41a;
  border-left-color: #52c41a;
  background: rgba(82, 196, 26, 0.1);
}

.stat-item.error {
  color: #F56C6C;
  border-left-color: #F56C6C;
  background: rgba(245, 108, 108, 0.1);
}

/* 右侧SQL结果面板 */
.sql-results-panel {
  flex: 1;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.sql-results-list {
  padding: 16px 16px 80px 16px; /* 大幅增加底部padding，避免被分页组件遮挡 */
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden; /* 防止水平滚动 */
  min-height: 0; /* 重要：确保flex子元素可以收缩 */
  box-sizing: border-box; /* 确保padding包含在尺寸计算中 */
  height: 0; /* 强制flex子元素使用flex计算的高度 */
}

.sql-result-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px 0;
  border-bottom: 1px solid #f0f0f0;
  min-height: 80px; /* 确保每项有足够高度 */
}

.sql-result-item:last-child {
  border-bottom: none;
  margin-bottom: 20px; /* 为最后一项添加额外间距 */
}

.sql-result-item.success .sql-text {
  border-left: 3px solid #52c41a;
}

.sql-result-item.error .sql-text {
  border-left: 3px solid #F56C6C;
}

/* SQL索引样式 */
.sql-index {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  min-width: 40px;
  padding-top: 4px;
}

.status-icon {
  font-size: 18px;
}

.index-number {
  font-weight: 600;
  color: #909399;
  font-size: 12px;
}

/* SQL内容样式 */
.sql-content {
  flex: 1;
  min-width: 0;
}

.sql-text {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  color: #303133;
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 8px;
  word-break: break-word;
  white-space: pre-wrap;
  overflow-wrap: break-word;
  line-height: 1.5;
}

.result-info {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 12px;
}

.affected-rows {
  color: #67C23A;
  font-weight: 500;
}

.error-message {
  color: #F56C6C;
  font-weight: 500;
  word-break: break-word;
}

/* 响应式设计优化 */
@media (max-width: 1200px) {
  .multi-sql-execution-layout {
    flex-direction: column;
    height: auto;
    max-height: none;
    min-height: auto;
  }

  .execution-summary-panel {
    flex: none;
    min-width: auto;
  }

  .sql-results-panel {
    height: 400px;
    max-height: 50vh;
  }

  .top-toolbar {
    padding: 6px 12px;
    min-height: 40px;
  }

  .edit-actions .el-button {
    padding: 6px 12px;
    font-size: 12px;
    height: 28px;
  }

  .results-container {
    margin: 8px 8px 16px 8px;
  }

  .pagination-container {
    padding: 8px 12px;
    min-height: 50px;
  }

  .result-pagination {
    padding: 6px 8px;
    min-height: 44px;
    height: 44px;
  }

  .tab-item {
    padding: 4px 8px;
    font-size: 11px;
  }

  .result-stats {
    padding: 4px 8px;
    font-size: 11px;
    min-height: 24px;
  }

  .sql-text {
    font-size: 12px;
  }
}

/* 中等屏幕尺寸优化 */
@media (max-width: 1400px) {
  .multi-sql-execution-layout {
    margin: 8px;
  }

  .execution-summary-panel {
    flex: 0 0 280px;
  }
}

/* 表格行高优化 */
.result-panel .el-table td {
  padding: 6px 8px;
  line-height: 1.4;
}

.result-panel .el-table th {
  padding: 8px;
  line-height: 1.4;
}

/* 数据源选择器优化 */
.datasource-selectors .el-select {
  min-width: 120px;
}

.datasource-selectors .el-select .el-input {
  height: 28px;
}

.datasource-selectors .el-select .el-input__wrapper {
  padding: 0 8px;
  font-size: 12px;
}

.tab-actions {
  display: flex;
  align-items: center;
}

/* 面板样式 */
.output-panel {
  flex: 1;
  overflow: auto;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  height: 100%;
  padding: 0;
  margin: 0;
}

.output-content {
  flex: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #1e1e1e;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', monospace;
  font-size: 12px;
  height: 100%;
  line-height: 1.5;
}

.output-item {
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
}

.timestamp {
  color: #569cd6; /* 蓝色时间戳 */
  font-size: 11px;
  margin-bottom: 2px;
  font-weight: 500;
}

.content {
  padding: 2px;
  white-space: pre-wrap;
  word-break: break-all;
}

.success-output {
  color: #6a9955; /* 绿色成功输出 */
}

.error-output {
  color: #f14c4c; /* 红色错误输出 */
}

.pending-output {
  color: #dcdcaa; /* 黄色等待输出 */
}

.result-text {
  margin-left: 8px;
}

.clickhouse-badge {
  display: flex;
  align-items: center;
}

.clickhouse-warning {
  margin-top: 15px;
  width: 100%;
  max-width: 500px;
}

.admin-actions {
  display: flex;
  align-items: center;
}

.ml-2 {
  margin-left: 8px;
}

.executing-container {
  margin: 10px 0;
}

/* 添加取消操作按钮的样式 */
.cancel-query-actions {
  margin-top: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.cancel-query-actions .el-checkbox {
  margin-left: 0;
  font-size: 13px;
  color: #606266;
}

/* 修正表格样式 */
.el-table {
  flex: 1;
}

/* 确保表格容器有正确的滚动行为 */
.result-panel {
  position: relative;
}

.result-panel .editable-result-table {
  position: relative;
  overflow-y: auto; /* 垂直滚动 */
  overflow-x: auto; /* 水平滚动 */
}



.el-table-column--center .cell {
  padding-left: 5px !important;
  padding-right: 5px !important;
}

:deep(.el-empty__description) {
  margin-top: 10px;
}

:deep(.el-empty__description p) {
  color: #909399;
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 0;
}

:deep(.el-select) {
  width: 200px;
}

.pagination-container {
  padding: 12px 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-top: 1px solid #ebeef5;
  background: #ffffff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.06);
  flex-shrink: 0; /* 防止被压缩 */
  min-height: 60px; /* 确保有足够高度 */
  position: sticky; /* 改回sticky定位 */
  bottom: 0;
  z-index: 100; /* 提高层级 */
  margin-top: auto; /* 自动推到底部 */
}

.resizer {
  height: 5px; /* 减小高度 */
  cursor: row-resize;
  background-color: #f2f2f2;
  border-top: 1px solid #dcdfe6;
  border-bottom: 1px solid #dcdfe6;
  margin: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: background-color 0.2s;
  position: relative;
  user-select: none;
  touch-action: none;
  z-index: 10;
}

.resizer:hover, .resizer.active {
  background-color: #e6f1fc;
}

.resizer::after {
  content: '';
  width: 30px;
  height: 4px;
  background-color: #c0c4cc;
  border-radius: 2px;
}

:deep(.el-table) {
  flex: 1;
}

:deep(.el-table th) {
  white-space: nowrap;
  overflow: hidden;
  user-select: none;
  background-color: #f5f7fa;
  padding: 4px 0;
  text-align: center;
}

:deep(.el-table td) {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 90px;
  cursor: pointer;
  padding: 4px 0;
}

:deep(.el-table tr:hover td) {
  background-color: #f5f7fa;
}

.cell-detail-content {
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
  max-height: 400px;
  overflow: auto;
}

.cell-detail-content pre {
  margin: 0;
  white-space: pre-wrap;
  word-break: break-word;
  font-family: Consolas, Monaco, monospace;
  line-height: 1.5;
}

/* 自动补全菜单样式 */
:deep(.cm-tooltip.cm-tooltip-autocomplete) {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

:deep(.cm-tooltip-autocomplete ul) {
  font-family: 'Consolas', 'Monaco', 'Andale Mono', 'Ubuntu Mono', monospace;
  max-height: 300px !important;
}

:deep(.cm-tooltip-autocomplete li) {
  display: flex;
  align-items: center;
  padding: 6px 10px !important;
}

:deep(.cm-completionIcon) {
  display: inline-block;
  margin-right: 8px;
  width: 20px;
  text-align: center;
}

:deep(.cm-completionIcon-table) {
  color: #409eff;
}

:deep(.cm-completionIcon-field) {
  color: #67c23a;
}

:deep(.cm-completionIcon-keyword) {
  color: #e6a23c;
}

:deep(.cm-completionIcon-function) {
  color: #f56c6c;
}

:deep(.cm-completionIcon-operator) {
  color: #e6a23c;
}

:deep(.cm-completionIcon-type) {
  color: #22863a;
}

:deep(.cm-completionDetail) {
  margin-left: 4px;
  color: #909399;
  font-size: 0.85em;
}

.cm-completion-icon-schema { color: #6f42c1; }
.cm-completion-icon-table { color: #005cc5; }
.cm-completion-icon-column { color: #22863a; }
.cm-completion-icon-keyword { color: #d73a49; }
.cm-completion-option-wrapper { display: flex; align-items: center; width: 100%; }
.cm-completion-label { flex-grow: 1; }
.cm-completion-detail { margin-left: 1em; color: #6a737d; font-size: 0.9em; }

/* 新增：自动补全样式 */
:deep(.completion-keyword) {
  color: #d4a747;
  font-weight: bold;
}

:deep(.completion-schema) {
  color: #3498db;
  font-weight: bold;
}

:deep(.completion-table) {
  color: #2ecc71;
  font-weight: bold;
}

:deep(.completion-column) {
  color: #9b59b6;
}

:deep(.completion-function) {
  color: #e67e22;
  font-style: italic;
}

:deep(.completion-operator) {
  color: #f1c40f;
  font-weight: bold;
}

:deep(.completion-type) {
  color: #16a085;
}

:deep(.sql-completion-tooltip) {
  max-height: 400px !important;
  overflow-y: auto;
}

:deep(.cm-tooltip.cm-completionInfo) {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
  max-width: 400px;
  max-height: 300px;
  overflow: auto;
}

/* 执行按钮样式增强 */
:deep(.el-button.is-loading) {
  background-color: #409eff;
  color: white;
  opacity: 0.8;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0.6;
  }
}

/* 新增编辑模式相关样式 */
.edit-mode-switch {
  margin-right: 15px;
}

.cell-edit-content {
  padding: 15px;
}

:deep(.el-input__inner), :deep(.el-textarea__inner) {
  font-family: Consolas, Monaco, monospace;
}

/* 编辑模式下表格样式增强 */
:deep(.el-table.edit-mode td) {
  cursor: pointer;
  transition: background-color 0.2s;
}

:deep(.el-table.edit-mode td:hover) {
  background-color: #e6f1fc !important;
}

:deep(.el-table.edit-mode td::after) {
  content: "✎";
  position: absolute;
  top: 2px;
  right: 2px;
  font-size: 12px;
  color: #409eff;
  opacity: 0;
  transition: opacity 0.2s;
}

:deep(.el-table.edit-mode td:hover::after) {
  opacity: 1;
}

.null-value {
  color: #909399;
  font-style: italic;
}

.null-text {
  color: #909399;
  font-style: italic;
}

.cell-content {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 18px;
  padding: 2px;
  line-height: normal;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-bottom: 1px solid #dcdfe6;
  max-width: 100%;
}

.action-left {
  display: flex;
  align-items: center;
}

.table-info {
  display: flex;
  align-items: center;
  font-size: 13px;
  color: #606266;
  margin-left: auto;
}

.table-count {
  font-size: 13px;
  font-weight: 500;
}

.compact-table-header :deep(.table-actions) {
  padding: 2px 8px;
  height: 30px;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 2px;
}

.compact-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 8px;
  height: 30px;
  background-color: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 2px;
}

.empty-actions {
  margin-top: 15px;
  display: flex;
  justify-content: center;
}

/* 空结果面板样式 - 已整合到 result-panel 中 */

.empty-result-content {
  padding: 30px;
  text-align: center;
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  background-color: #f8f9fa;
}

.empty-icon {
  font-size: 48px;
  color: #c0c4cc;
  margin-bottom: 15px;
}

.empty-text {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

/* Add style for the backup SQL display */
.backup-sql-display {
  margin-top: 15px;
  padding: 10px;
  background-color: #f5f7fa;
  border: 1px solid #e9e9eb;
  border-radius: 4px;
}
.backup-sql-display pre {
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: monospace;
}

/* 查询执行中状态样式 */
.executing-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

.executing-overlay {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
}

.executing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 20px 40px;
  border-radius: 8px;
  border: 1px solid rgba(64, 158, 255, 0.2);
  pointer-events: auto;
}

.executing-icon {
  font-size: 32px;
  color: #409eff;
  margin-bottom: 10px;
}

.executing-text {
  font-size: 18px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 5px;
}

.executing-subtext {
  font-size: 14px;
  color: #606266;
}

/* 选中SQL执行指示器样式 */
.selected-sql-indicator {
  font-size: 12px;
  margin-left: 4px;
  color: #fff;
  font-weight: normal;
  opacity: 0.9;
}

.empty-edit-toolbar {
  margin-top: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.edit-status-text {
  color: #409EFF;
  font-size: 14px;
}

/* 过渡动画 */
.tab-fade-enter-active,
.tab-fade-leave-active {
  transition: all 0.2s ease;
}

.tab-fade-enter-from,
.tab-fade-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.tab-fade-move {
  transition: transform 0.2s ease;
}



/* DML/DDL结果美化样式 */
.dml-ddl-result-alert {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  margin: 32px 0;
}
.dml-ddl-card {
  min-width: 420px;
  max-width: 900px;
  background: #f6f9f6;
  border: 1px solid #e1eecf;
  border-radius: 10px;
  box-shadow: 0 2px 12px 0 rgba(103,194,58,0.08);
  padding: 24px 32px 18px 32px;
}
.dml-ddl-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}
.dml-ddl-icon {
  margin-right: 10px;
}
.dml-ddl-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
}
.dml-ddl-sql-row {
  margin-bottom: 10px;
  display: flex;
  align-items: center;
}
.dml-ddl-sql-label {
  color: #888;
  font-size: 14px;
  margin-right: 6px;
}
.dml-ddl-sql-text {
  font-family: 'Fira Mono', 'Consolas', 'Menlo', monospace;
  font-size: 14px;
  color: #444;
  background: #f3f3f3;
  border-radius: 3px;
  padding: 2px 6px;
  max-width: 600px;
  display: inline-block;
  white-space: pre;
  overflow: hidden;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.dml-ddl-rowcount-row {
  margin-top: 6px;
  font-size: 15px;
}
.dml-ddl-rowcount-label {
  color: #888;
  margin-right: 6px;
}
.dml-ddl-rowcount-value {
  font-weight: bold;
  font-size: 16px;
}

/* 滚动条样式 */

.summary-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.summary-icon {
  font-size: 20px;
  margin-right: 8px;
}

.summary-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-right: 12px;
}



/* 自定义滚动条样式 */
.sql-results-list::-webkit-scrollbar {
  width: 10px; /* 进一步增加宽度，确保可见 */
}

.sql-results-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 5px;
  border: 1px solid #e9ecef;
}

.sql-results-list::-webkit-scrollbar-thumb {
  background: #409EFF;
  border-radius: 5px;
  border: 1px solid #ffffff;
}

.sql-results-list::-webkit-scrollbar-thumb:hover {
  background: #337ecc;
}

/* 确保滚动容器正确工作 */
.sql-results-panel {
  position: relative;
}

.sql-results-list {
  position: relative;
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: #c1c1c1 #f1f1f1; /* Firefox */
}







.sql-code {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 13px;
  background: #f5f7fa;
  padding: 8px 12px;
  border-radius: 4px;
  color: #409EFF;
  display: block;
  word-break: break-all;
  white-space: pre-wrap;
  border: 1px solid #e9ecef;
}

.sql-result {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.result-status {
  font-weight: 600;
  font-size: 14px;
}

.result-status.success {
  color: #67C23A;
}

.result-status.error {
  color: #F56C6C;
}

.result-message {
  color: #909399;
  font-size: 13px;
}

.result-error {
  color: #F56C6C;
  font-size: 13px;
  font-weight: 500;
}

/* 修复多条SQL执行结果只显示部分的问题，强制高度自适应和可滚动 */
.results-container {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  min-height: 0;
}
.multi-sql-execution-layout {
  display: flex;
  flex-direction: row;
  width: 100%;
  flex: 1 1 0%;
  height: 100%;
  min-height: 0;
}
.execution-summary-panel {
  flex: 0 0 320px;
  min-width: 260px;
  max-width: 400px;
  background: #f8f9fa;
  border-right: 1px solid #e9ecef;
  border-radius: 8px 0 0 8px;
  margin-right: 0;
  height: 100%;
  min-height: 0;
  display: flex;
  flex-direction: column;
}
.sql-results-panel {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border: 1px solid #e9ecef;
  border-radius: 0 8px 8px 0;
  overflow: hidden;
  height: 200px;
  min-height: 0;
}
.sql-results-list {
  flex: 1 1 0%;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: 100%;
  padding: 16px 16px 80px 16px;
  box-sizing: border-box;
}


/* 让多条SQL执行结果页的右侧结果区始终max-height: 80vh，overflow-y: auto，不依赖外部窗口高度 */
.multi-sql-execution-layout .sql-results-list {
  max-height: 80vh;
  overflow-y: auto;
  overflow-x: hidden;
  min-height: 0;
  height: auto;
  padding: 16px 16px 80px 16px;
  box-sizing: border-box;
  background: #fff;
  position: static;
}

/* 独立分页组件样式 - 固定在底部 */
.independent-pagination-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background-color: #fafafa;
  border-top: 1px solid #d9d9d9;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  padding: 0 1000px;
  box-sizing: border-box;
}

/* 确保主容器为分页组件预留空间 */
.query-panel {
  padding-bottom: 80px; /* 为固定分页组件预留空间 */
}

/* DMS风格分页组件样式 */
.independent-pagination-container .el-pagination {
  --el-pagination-font-size: 13px;
  --el-pagination-bg-color: #ffffff;
  --el-pagination-text-color: #666666;
  --el-pagination-border-radius: 4px;
}

.independent-pagination-container .el-pagination .el-pager li {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
  margin: 0 2px;
  border-radius: 4px;
}

.independent-pagination-container .el-pagination .el-pager li:hover {
  background-color: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.independent-pagination-container .el-pagination .el-pager li.is-active {
  background-color: #1890ff;
  border-color: #1890ff;
  color: #ffffff;
}

/* DDL操作成功提示样式 */
.ddl-success-message {
  padding: 20px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #e1f5fe;
}

.ddl-success-message .el-result {
  padding: 0;
}

.ddl-success-message .el-result__title {
  color: #1976d2;
  font-weight: 600;
}

.ddl-success-message .el-result__subtitle {
  color: #424242;
  margin-top: 8px;
}

/* 欢迎界面样式 */
.welcome-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
  padding: 40px 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  margin: 20px 0;
}

.welcome-content {
  text-align: center;
  max-width: 600px;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.welcome-icon {
  font-size: 48px;
  color: #409EFF;
  margin-bottom: 20px;
}

.welcome-title {
  color: #303133;
  margin-bottom: 16px;
  font-size: 24px;
  font-weight: 600;
}

.welcome-description {
  color: #606266;
  margin-bottom: 30px;
  font-size: 16px;
  line-height: 1.6;
}

.welcome-features {
  margin-bottom: 30px;
}

.feature-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  padding: 8px 0;
  color: #606266;
}

.feature-item .el-icon {
  margin-right: 12px;
  color: #409EFF;
  font-size: 18px;
}

.welcome-tips {
  text-align: left;
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409EFF;
}

.welcome-tips p {
  margin: 0 0 12px 0;
  color: #303133;
  font-weight: 600;
}

.welcome-tips ul {
  margin: 0;
  padding-left: 20px;
  color: #606266;
}

.welcome-tips li {
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 多SQL执行结果消息样式优化 */
.sql-result-item .result-info .success-message {
  color: #67c23a;
  font-weight: 500;
  font-size: 13px;
}

.sql-result-item .result-info .error-message {
  color: #f56c6c;
  font-weight: 500;
  font-size: 13px;
}

/* 原有的affected-rows样式保持兼容 */
.sql-result-item .result-info .affected-rows {
  color: #67c23a;
  font-weight: 500;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .independent-pagination-container {
    height: 50px;
    padding: 0 10px;
  }

  .query-panel {
    padding-bottom: 50px;
  }
}

</style>